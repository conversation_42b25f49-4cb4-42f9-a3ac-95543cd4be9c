/**
 * 数学公式渲染服务
 * 基于KaTeX提供数学公式渲染功能
 */

import katex from 'katex'
import 'katex/dist/katex.min.css'

/**
 * 数学公式渲染配置接口
 */
export interface MathRenderConfig {
  displayMode: boolean      // 是否为块级公式
  throwOnError: boolean     // 遇到错误是否抛出异常
  errorColor: string        // 错误颜色
  macros: Record<string, string> // 自定义宏
  strict: boolean | string  // 严格模式
  trust: boolean           // 是否信任输入
  fleqn: boolean           // 左对齐公式
  leqno: boolean           // 公式编号在左侧
  minRuleThickness: number // 最小规则厚度
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: MathRenderConfig = {
  displayMode: false,
  throwOnError: false,
  errorColor: '#cc0000',
  macros: {},
  strict: 'warn',
  trust: false,
  fleqn: false,
  leqno: false,
  minRuleThickness: 0.04
}

/**
 * 常用数学宏定义
 */
const COMMON_MACROS: Record<string, string> = {
  // 集合论
  '\\N': '\\mathbb{N}',
  '\\Z': '\\mathbb{Z}',
  '\\Q': '\\mathbb{Q}',
  '\\R': '\\mathbb{R}',
  '\\C': '\\mathbb{C}',
  
  // 向量
  '\\vec': '\\overrightarrow{#1}',
  '\\norm': '\\left\\|#1\\right\\|',
  
  // 微积分
  '\\diff': '\\mathrm{d}',
  '\\pdiff': '\\partial',
  
  // 概率论
  '\\Pr': '\\mathbb{P}',
  '\\E': '\\mathbb{E}',
  '\\Var': '\\mathrm{Var}',
  
  // 线性代数
  '\\rank': '\\mathrm{rank}',
  '\\tr': '\\mathrm{tr}',
  '\\det': '\\mathrm{det}',
  
  // 其他常用
  '\\argmax': '\\mathop{\\mathrm{argmax}}',
  '\\argmin': '\\mathop{\\mathrm{argmin}}'
}

/**
 * 数学公式渲染服务类
 */
export class MathRendererService {
  private static instance: MathRendererService
  private config: MathRenderConfig
  private cache = new Map<string, string>()
  private maxCacheSize = 1000

  /**
   * 获取服务实例
   */
  public static getInstance(): MathRendererService {
    if (!MathRendererService.instance) {
      MathRendererService.instance = new MathRendererService()
    }
    return MathRendererService.instance
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this.config = {
      ...DEFAULT_CONFIG,
      macros: { ...COMMON_MACROS }
    }
  }

  /**
   * 渲染行内数学公式
   * @param latex LaTeX公式字符串
   * @param options 渲染选项
   * @returns 渲染后的HTML字符串
   */
  public renderInline(latex: string, options: Partial<MathRenderConfig> = {}): string {
    const config = {
      ...this.config,
      ...options,
      displayMode: false
    }
    
    return this.render(latex, config)
  }

  /**
   * 渲染块级数学公式
   * @param latex LaTeX公式字符串
   * @param options 渲染选项
   * @returns 渲染后的HTML字符串
   */
  public renderBlock(latex: string, options: Partial<MathRenderConfig> = {}): string {
    const config = {
      ...this.config,
      ...options,
      displayMode: true
    }
    
    return this.render(latex, config)
  }

  /**
   * 渲染数学公式
   * @param latex LaTeX公式字符串
   * @param config 渲染配置
   * @returns 渲染后的HTML字符串
   */
  private render(latex: string, config: MathRenderConfig): string {
    try {
      // 清理输入
      const cleanLatex = this.cleanLatex(latex)
      if (!cleanLatex.trim()) {
        return ''
      }

      // 检查缓存
      const cacheKey = this.getCacheKey(cleanLatex, config)
      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey)!
      }

      // 渲染公式
      const html = katex.renderToString(cleanLatex, {
        displayMode: config.displayMode,
        throwOnError: config.throwOnError,
        errorColor: config.errorColor,
        macros: config.macros,
        strict: config.strict,
        trust: config.trust,
        fleqn: config.fleqn,
        leqno: config.leqno,
        minRuleThickness: config.minRuleThickness
      })

      // 添加包装器
      const wrappedHtml = this.wrapRenderedMath(html, config.displayMode)

      // 缓存结果
      this.cacheResult(cacheKey, wrappedHtml)

      return wrappedHtml
    } catch (error) {
      console.error('数学公式渲染失败:', error)
      return this.renderError(latex, error as Error, config.displayMode)
    }
  }

  /**
   * 渲染页面中的所有数学公式
   * @param container 容器元素
   */
  public renderAllInContainer(container: HTMLElement): void {
    // 渲染行内公式 $...$
    this.renderInlineFormulas(container)
    
    // 渲染块级公式 $$...$$
    this.renderBlockFormulas(container)
    
    // 渲染带类名的公式元素
    this.renderClassBasedFormulas(container)
  }

  /**
   * 渲染行内公式
   */
  private renderInlineFormulas(container: HTMLElement): void {
    const inlineElements = container.querySelectorAll('.math-formula.inline-math')
    
    inlineElements.forEach(element => {
      const latex = element.textContent || ''
      if (latex.trim()) {
        try {
          const html = this.renderInline(latex)
          element.innerHTML = html
          element.classList.add('math-rendered')
        } catch (error) {
          console.error('行内公式渲染失败:', error)
          element.classList.add('math-error')
        }
      }
    })
  }

  /**
   * 渲染块级公式
   */
  private renderBlockFormulas(container: HTMLElement): void {
    const blockElements = container.querySelectorAll('.math-formula.block-math')
    
    blockElements.forEach(element => {
      const latex = element.textContent || ''
      if (latex.trim()) {
        try {
          const html = this.renderBlock(latex)
          element.innerHTML = html
          element.classList.add('math-rendered')
        } catch (error) {
          console.error('块级公式渲染失败:', error)
          element.classList.add('math-error')
        }
      }
    })
  }

  /**
   * 渲染基于类名的公式
   */
  private renderClassBasedFormulas(container: HTMLElement): void {
    const mathElements = container.querySelectorAll('[data-math]')
    
    mathElements.forEach(element => {
      const latex = element.getAttribute('data-math') || element.textContent || ''
      const isBlock = element.classList.contains('math-block') || 
                     element.tagName.toLowerCase() === 'div'
      
      if (latex.trim()) {
        try {
          const html = isBlock ? this.renderBlock(latex) : this.renderInline(latex)
          element.innerHTML = html
          element.classList.add('math-rendered')
        } catch (error) {
          console.error('公式渲染失败:', error)
          element.classList.add('math-error')
        }
      }
    })
  }

  /**
   * 清理LaTeX输入
   */
  private cleanLatex(latex: string): string {
    return latex
      .trim()
      .replace(/^\$+|\$+$/g, '') // 移除开头和结尾的$符号
      .replace(/\\\\/g, '\\\\') // 确保换行符正确转义
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(latex: string, config: MathRenderConfig): string {
    const configHash = JSON.stringify({
      displayMode: config.displayMode,
      macros: config.macros,
      strict: config.strict,
      fleqn: config.fleqn,
      leqno: config.leqno
    })
    
    return `${latex}|${configHash}`
  }

  /**
   * 缓存渲染结果
   */
  private cacheResult(key: string, html: string): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(key, html)
  }

  /**
   * 包装渲染后的数学公式
   */
  private wrapRenderedMath(html: string, isBlock: boolean): string {
    const className = isBlock ? 'katex-display-wrapper' : 'katex-inline-wrapper'
    return `<span class="${className}">${html}</span>`
  }

  /**
   * 渲染错误信息
   */
  private renderError(latex: string, error: Error, isBlock: boolean): string {
    const errorClass = isBlock ? 'math-error-block' : 'math-error-inline'
    const errorMessage = error.message || '公式渲染错误'
    
    return `<span class="math-error ${errorClass}" title="${errorMessage}">
      <span class="math-error-icon">⚠️</span>
      <span class="math-error-latex">${this.escapeHtml(latex)}</span>
    </span>`
  }

  /**
   * 转义HTML字符
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 更新配置
   * @param newConfig 新配置
   */
  public updateConfig(newConfig: Partial<MathRenderConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 清空缓存，因为配置变更可能影响渲染结果
    this.cache.clear()
  }

  /**
   * 添加自定义宏
   * @param macros 宏定义
   */
  public addMacros(macros: Record<string, string>): void {
    this.config.macros = { ...this.config.macros, ...macros }
    this.cache.clear()
  }

  /**
   * 获取当前配置
   */
  public getConfig(): MathRenderConfig {
    return { ...this.config }
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    this.cache.clear()
    console.log('数学公式渲染缓存已清空')
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats(): { size: number; maxSize: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize
    }
  }

  /**
   * 验证LaTeX语法
   * @param latex LaTeX字符串
   * @returns 验证结果
   */
  public validateLatex(latex: string): { valid: boolean; error?: string } {
    try {
      katex.renderToString(latex, {
        displayMode: false,
        throwOnError: true,
        strict: 'error'
      })
      return { valid: true }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }
}

/**
 * 导出服务实例
 */
export const mathRendererService = MathRendererService.getInstance()

/**
 * 便捷函数
 */
export const renderInlineMath = (latex: string, options?: Partial<MathRenderConfig>) => {
  return mathRendererService.renderInline(latex, options)
}

export const renderBlockMath = (latex: string, options?: Partial<MathRenderConfig>) => {
  return mathRendererService.renderBlock(latex, options)
}

export const renderAllMath = (container: HTMLElement) => {
  mathRendererService.renderAllInContainer(container)
}
