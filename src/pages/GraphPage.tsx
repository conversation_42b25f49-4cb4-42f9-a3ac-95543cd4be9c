import React from 'react'
import { Card, Typography, Button } from 'antd'
import { ShareAltOutlined, SaveOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

/**
 * 关系图谱页面组件（占位）
 * 后续将实现完整的关系图谱功能
 */
const GraphPage: React.FC = () => {
  return (
    <div className="h-full p-6">
      <Card className="h-full">
        <div className="text-center py-20">
          <ShareAltOutlined className="text-6xl text-cyan-500 mb-4" />
          <Title level={2}>关系图谱</Title>
          <Paragraph className="text-gray-600 mb-6">
            这里将实现强大的关系图谱功能，包括：
          </Paragraph>
          <div className="text-left max-w-md mx-auto mb-8">
            <ul className="text-gray-600 space-y-2">
              <li>• 可视化知识网络图</li>
              <li>• 节点和连接交互</li>
              <li>• 图谱筛选和搜索</li>
              <li>• 社区检测分析</li>
              <li>• 多种布局算法</li>
              <li>• 图谱导出功能</li>
            </ul>
          </div>
          <Button 
            type="primary" 
            size="large"
            icon={<SaveOutlined />}
            disabled
          >
            功能开发中...
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default GraphPage
