/**
 * 应用核心类型定义
 * 定义了多维度笔记应用的基础数据结构和接口
 */

// 文档类型枚举
export enum DocumentType {
  TEXT = 'text',           // 文本笔记
  WHITEBOARD = 'whiteboard', // 白板
  MINDMAP = 'mindmap',     // 思维导图
  KANBAN = 'kanban'        // 看板
}

// 链接类型枚举
export enum LinkType {
  REFERENCE = 'reference',     // 引用
  EMBED = 'embed',            // 嵌入
  RELATED = 'related',        // 相关
  PARENT_CHILD = 'parent_child' // 父子关系
}

// 基础文档元数据接口
export interface DocumentMetadata {
  author?: string         // 作者
  description?: string    // 描述
  keywords?: string[]     // 关键词
  version: number         // 版本号
  size: number           // 文档大小（字节）
  checksum: string       // 校验和
  lastModifiedBy?: string // 最后修改者
  wordCount?: number     // 字数统计
  readingTime?: number   // 预计阅读时间（分钟）
}

// 基础文档接口
export interface BaseDocument {
  id: string                    // 唯一标识符
  title: string                 // 文档标题
  type: DocumentType            // 文档类型
  content: unknown              // 文档内容（根据类型不同）
  metadata: DocumentMetadata    // 元数据
  createdAt: Date              // 创建时间
  updatedAt: Date              // 更新时间
  tags: string[]               // 标签
  links: DocumentLink[]        // 关联链接
  isDeleted?: boolean          // 是否已删除（软删除）
  parentId?: string            // 父文档ID（用于层级结构）
  order?: number               // 排序序号
}

// 文档链接关系接口
export interface DocumentLink {
  id: string             // 链接ID
  sourceId: string       // 源文档ID
  targetId: string       // 目标文档ID
  type: LinkType         // 链接类型
  label?: string         // 链接标签
  metadata?: Record<string, unknown> // 链接元数据
  createdAt: Date        // 创建时间
  updatedAt: Date        // 更新时间
}

// 文本文档内容接口
export interface TextContent {
  markdown: string        // Markdown内容
  html?: string          // 渲染后的HTML
  outline: OutlineNode[] // 文档大纲
  lastCursorPosition?: number // 最后光标位置
}

// 文档大纲节点接口
export interface OutlineNode {
  id: string             // 节点ID
  title: string          // 标题
  level: number          // 标题级别 (1-6)
  line: number           // 行号
  children: OutlineNode[] // 子节点
}

// 文本文档接口
export interface TextDocument extends BaseDocument {
  type: DocumentType.TEXT
  content: TextContent
}

// 白板内容接口
export interface WhiteboardContent {
  canvas: CanvasData     // 画布数据
  objects: FabricObject[] // 绘图对象
  viewport: Viewport     // 视口信息
  layers: Layer[]        // 图层信息
}

// 画布数据接口
export interface CanvasData {
  width: number          // 画布宽度
  height: number         // 画布高度
  backgroundColor: string // 背景颜色
  backgroundImage?: string // 背景图片
  zoom: number           // 缩放比例
}

// Fabric.js对象接口（简化版）
export interface FabricObject {
  id: string             // 对象ID
  type: string           // 对象类型
  left: number           // X坐标
  top: number            // Y坐标
  width: number          // 宽度
  height: number         // 高度
  angle: number          // 旋转角度
  scaleX: number         // X轴缩放
  scaleY: number         // Y轴缩放
  fill?: string          // 填充颜色
  stroke?: string        // 边框颜色
  strokeWidth?: number   // 边框宽度
  opacity: number        // 透明度
  visible: boolean       // 是否可见
  selectable: boolean    // 是否可选择
  layerId: string        // 所属图层ID
  metadata?: Record<string, unknown> // 自定义元数据
}

// 视口信息接口
export interface Viewport {
  x: number              // 视口X偏移
  y: number              // 视口Y偏移
  zoom: number           // 缩放级别
}

// 图层接口
export interface Layer {
  id: string             // 图层ID
  name: string           // 图层名称
  visible: boolean       // 是否可见
  locked: boolean        // 是否锁定
  opacity: number        // 透明度
  order: number          // 层级顺序
}

// 白板文档接口
export interface WhiteboardDocument extends BaseDocument {
  type: DocumentType.WHITEBOARD
  content: WhiteboardContent
}

// 思维导图内容接口
export interface MindmapContent {
  nodes: MindmapNode[]   // 节点数据
  edges: MindmapEdge[]   // 连接数据
  layout: LayoutConfig   // 布局配置
  viewport: Viewport     // 视口信息
}

// 思维导图节点接口
export interface MindmapNode {
  id: string             // 节点ID
  label: string          // 节点标签
  x: number              // X坐标
  y: number              // Y坐标
  width: number          // 宽度
  height: number         // 高度
  type: string           // 节点类型
  style: NodeStyle       // 节点样式
  data?: Record<string, unknown> // 节点数据
  parentId?: string      // 父节点ID
  collapsed?: boolean    // 是否折叠
}

// 思维导图连接接口
export interface MindmapEdge {
  id: string             // 连接ID
  source: string         // 源节点ID
  target: string         // 目标节点ID
  type: string           // 连接类型
  style: EdgeStyle       // 连接样式
  label?: string         // 连接标签
}

// 节点样式接口
export interface NodeStyle {
  backgroundColor: string // 背景颜色
  borderColor: string    // 边框颜色
  borderWidth: number    // 边框宽度
  borderRadius: number   // 圆角半径
  fontSize: number       // 字体大小
  fontColor: string      // 字体颜色
  fontWeight: string     // 字体粗细
  padding: number        // 内边距
}

// 连接样式接口
export interface EdgeStyle {
  stroke: string         // 线条颜色
  strokeWidth: number    // 线条宽度
  strokeDasharray?: string // 虚线样式
  markerEnd?: string     // 箭头样式
}

// 布局配置接口
export interface LayoutConfig {
  type: string           // 布局类型 (tree, radial, force, etc.)
  direction: string      // 布局方向 (TB, BT, LR, RL)
  nodeSpacing: number    // 节点间距
  levelSpacing: number   // 层级间距
  alignment: string      // 对齐方式
}

// 思维导图文档接口
export interface MindmapDocument extends BaseDocument {
  type: DocumentType.MINDMAP
  content: MindmapContent
}

// 看板内容接口
export interface KanbanContent {
  columns: KanbanColumn[] // 列数据
  cards: KanbanCard[]     // 卡片数据
  settings: KanbanSettings // 看板设置
}

// 看板列接口
export interface KanbanColumn {
  id: string             // 列ID
  title: string          // 列标题
  order: number          // 排序序号
  color?: string         // 列颜色
  limit?: number         // 卡片数量限制
  collapsed?: boolean    // 是否折叠
}

// 看板卡片接口
export interface KanbanCard {
  id: string             // 卡片ID
  title: string          // 卡片标题
  description?: string   // 卡片描述
  columnId: string       // 所属列ID
  order: number          // 排序序号
  tags: string[]         // 标签
  priority: Priority     // 优先级
  dueDate?: Date         // 截止日期
  assignee?: string      // 负责人
  createdAt: Date        // 创建时间
  updatedAt: Date        // 更新时间
  attachments: string[]  // 附件ID列表
  checklist: ChecklistItem[] // 检查清单
}

// 优先级枚举
export enum Priority {
  LOW = 'low',           // 低优先级
  MEDIUM = 'medium',     // 中优先级
  HIGH = 'high',         // 高优先级
  URGENT = 'urgent'      // 紧急
}

// 检查清单项接口
export interface ChecklistItem {
  id: string             // 项目ID
  text: string           // 项目文本
  completed: boolean     // 是否完成
  order: number          // 排序序号
}

// 看板设置接口
export interface KanbanSettings {
  showCardCount: boolean // 显示卡片数量
  showColumnLimit: boolean // 显示列限制
  allowDragDrop: boolean // 允许拖拽
  cardTemplate?: string  // 卡片模板
  autoArchive: boolean   // 自动归档
  archiveDays: number    // 归档天数
}

// 看板文档接口
export interface KanbanDocument extends BaseDocument {
  type: DocumentType.KANBAN
  content: KanbanContent
}

// 联合类型：所有文档类型
export type Document = TextDocument | WhiteboardDocument | MindmapDocument | KanbanDocument

// 搜索结果接口
export interface SearchResult {
  document: Document     // 匹配的文档
  score: number          // 匹配分数
  highlights: string[]   // 高亮片段
  matchType: string      // 匹配类型 (title, content, tags)
}

// 搜索选项接口
export interface SearchOptions {
  types?: DocumentType[] // 限制搜索的文档类型
  tags?: string[]        // 限制搜索的标签
  dateRange?: {          // 日期范围
    start: Date
    end: Date
  }
  limit?: number         // 结果数量限制
  offset?: number        // 结果偏移量
  sortBy?: string        // 排序字段
  sortOrder?: 'asc' | 'desc' // 排序顺序
}

// 图谱数据接口
export interface GraphData {
  nodes: GraphNode[]     // 图谱节点
  edges: GraphEdge[]     // 图谱连接
  clusters?: GraphCluster[] // 图谱聚类
}

// 图谱节点接口
export interface GraphNode {
  id: string             // 节点ID
  label: string          // 节点标签
  type: DocumentType     // 节点类型
  size: number           // 节点大小
  color: string          // 节点颜色
  x?: number             // X坐标
  y?: number             // Y坐标
  degree: number         // 节点度数
  clusterId?: string     // 所属聚类ID
}

// 图谱连接接口
export interface GraphEdge {
  id: string             // 连接ID
  source: string         // 源节点ID
  target: string         // 目标节点ID
  type: LinkType         // 连接类型
  weight: number         // 连接权重
  color: string          // 连接颜色
  label?: string         // 连接标签
}

// 图谱聚类接口
export interface GraphCluster {
  id: string             // 聚类ID
  label: string          // 聚类标签
  nodes: string[]        // 包含的节点ID
  color: string          // 聚类颜色
  size: number           // 聚类大小
}

// 用户设置接口
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto' // 主题设置
  language: string       // 语言设置
  fontSize: number       // 字体大小
  autoSave: boolean      // 自动保存
  autoSaveInterval: number // 自动保存间隔（秒）
  showLineNumbers: boolean // 显示行号
  wordWrap: boolean      // 自动换行
  minimap: boolean       // 显示小地图
  spellCheck: boolean    // 拼写检查
  defaultDocumentType: DocumentType // 默认文档类型
  recentFilesLimit: number // 最近文件数量限制
}

// 导出格式枚举
export enum ExportFormat {
  MARKDOWN = 'markdown',
  HTML = 'html',
  PDF = 'pdf',
  JSON = 'json',
  ZIP = 'zip'
}

// 导入格式枚举
export enum ImportFormat {
  MARKDOWN = 'markdown',
  HTML = 'html',
  JSON = 'json',
  ZIP = 'zip',
  DOCX = 'docx'
}

// 同步结果接口
export interface SyncResult {
  success: boolean       // 同步是否成功
  message: string        // 同步消息
  conflictCount: number  // 冲突数量
  updatedCount: number   // 更新数量
  errorCount: number     // 错误数量
  timestamp: Date        // 同步时间戳
}
