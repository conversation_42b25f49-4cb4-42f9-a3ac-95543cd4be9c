import React from 'react'
import { Spin } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  tip?: string
  className?: string
  spinning?: boolean
  children?: React.ReactNode
}

/**
 * 加载动画组件
 * 提供统一的加载状态显示
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  tip = '加载中...',
  className = '',
  spinning = true,
  children
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: getSizeValue(size) }} spin />

  // 如果有子组件，则作为容器使用
  if (children) {
    return (
      <Spin 
        spinning={spinning} 
        tip={tip} 
        indicator={antIcon}
        className={className}
      >
        {children}
      </Spin>
    )
  }

  // 否则作为独立的加载指示器
  return (
    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
      <Spin 
        size={size} 
        tip={tip} 
        indicator={antIcon}
      />
    </div>
  )
}

/**
 * 根据尺寸获取对应的像素值
 */
function getSizeValue(size: 'small' | 'default' | 'large'): number {
  switch (size) {
    case 'small':
      return 14
    case 'large':
      return 24
    default:
      return 18
  }
}

export default LoadingSpinner
