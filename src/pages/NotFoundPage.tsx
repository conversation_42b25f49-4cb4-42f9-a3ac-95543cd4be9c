import React from 'react'
import { Result, Button } from 'antd'
import { useNavigate } from 'react-router-dom'
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons'

/**
 * 404页面组件
 * 当用户访问不存在的路由时显示
 */
const NotFoundPage: React.FC = () => {
  const navigate = useNavigate()

  const handleGoHome = () => {
    navigate('/')
  }

  const handleGoBack = () => {
    navigate(-1)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full">
        <Result
          status="404"
          title="404"
          subTitle="抱歉，您访问的页面不存在。"
          extra={[
            <Button 
              type="primary" 
              key="home"
              icon={<HomeOutlined />}
              onClick={handleGoHome}
            >
              返回首页
            </Button>,
            <Button 
              key="back"
              icon={<ArrowLeftOutlined />}
              onClick={handleGoBack}
            >
              返回上页
            </Button>,
          ]}
        />
      </div>
    </div>
  )
}

export default NotFoundPage
