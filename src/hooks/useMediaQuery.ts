import { useState, useEffect } from 'react'

/**
 * 媒体查询Hook
 * 用于响应式设计，监听屏幕尺寸变化
 * 
 * @param query - CSS媒体查询字符串
 * @returns 是否匹配媒体查询条件
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    // 检查浏览器是否支持matchMedia
    if (typeof window === 'undefined' || !window.matchMedia) {
      return
    }

    const mediaQuery = window.matchMedia(query)
    
    // 设置初始值
    setMatches(mediaQuery.matches)

    // 监听变化的处理函数
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    // 添加监听器
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange)
    } else {
      // 兼容旧版本浏览器
      mediaQuery.addListener(handleChange)
    }

    // 清理函数
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange)
      } else {
        // 兼容旧版本浏览器
        mediaQuery.removeListener(handleChange)
      }
    }
  }, [query])

  return matches
}

/**
 * 预定义的常用媒体查询Hook
 */
export const useBreakpoints = () => {
  const isMobile = useMediaQuery('(max-width: 767px)')
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)')
  const isDesktop = useMediaQuery('(min-width: 1024px)')
  const isLargeDesktop = useMediaQuery('(min-width: 1440px)')

  return {
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    // 组合条件
    isMobileOrTablet: isMobile || isTablet,
    isTabletOrDesktop: isTablet || isDesktop,
  }
}
