{"name": "multidimensional-notes", "version": "0.1.0", "description": "现代化的多维度笔记应用，支持文本、白板、思维导图、看板等多种格式，并提供强大的关联系统", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "dayjs": "^1.11.10", "@monaco-editor/react": "^4.6.0", "monaco-editor": "^0.45.0", "marked": "^11.1.1", "idb": "^8.0.0", "prismjs": "^1.29.0", "katex": "^0.16.9"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "@tailwindcss/typography": "^0.5.10", "@types/prismjs": "^1.26.3", "@types/katex": "^0.16.7"}, "keywords": ["notes", "markdown", "whiteboard", "mindmap", "kanban", "knowledge-management", "react", "typescript", "pwa"], "author": "Developer Team", "license": "MIT"}