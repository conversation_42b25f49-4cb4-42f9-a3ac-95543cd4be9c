import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { message, Spin } from 'antd'
import TextEditor from '@components/editor/TextEditor'
import { TextDocument, DocumentType, TextContent } from '@types/index'
import { documentDAO } from '@services/database/documentDAO'
import { generateUUID } from '@utils/index'

/**
 * 文本编辑器页面组件
 * 提供完整的Markdown文档编辑功能
 */
const TextEditorPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const [document, setDocument] = useState<TextDocument | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  /**
   * 加载文档
   */
  const loadDocument = useCallback(async (documentId: string) => {
    try {
      setLoading(true)
      const doc = await documentDAO.getById(documentId)

      if (doc && doc.type === DocumentType.TEXT) {
        setDocument(doc as TextDocument)
        console.log('文档加载成功:', doc.title)
      } else {
        message.error('文档不存在或类型不匹配')
        navigate('/')
      }
    } catch (error) {
      console.error('加载文档失败:', error)
      message.error('加载文档失败')
      navigate('/')
    } finally {
      setLoading(false)
    }
  }, [navigate])

  /**
   * 创建新文档
   */
  const createNewDocument = useCallback(async () => {
    try {
      setLoading(true)

      const newDocument: Omit<TextDocument, 'id' | 'createdAt' | 'updatedAt'> = {
        title: '新建文档',
        type: DocumentType.TEXT,
        content: {
          markdown: '',
          outline: []
        } as TextContent,
        metadata: {
          version: 1,
          size: 0,
          checksum: '',
          wordCount: 0,
          readingTime: 0
        },
        tags: [],
        links: []
      }

      const createdDoc = await documentDAO.create(newDocument)
      setDocument(createdDoc as TextDocument)

      // 更新URL
      navigate(`/editor/${createdDoc.id}`, { replace: true })

      console.log('新文档创建成功:', createdDoc.id)
    } catch (error) {
      console.error('创建文档失败:', error)
      message.error('创建文档失败')
    } finally {
      setLoading(false)
    }
  }, [navigate])

  /**
   * 保存文档
   */
  const handleSave = useCallback(async (updatedDocument: TextDocument) => {
    try {
      setSaving(true)

      // 更新元数据
      const content = updatedDocument.content.markdown
      const wordCount = content.length
      const readingTime = Math.ceil(wordCount / 200) // 假设每分钟200字

      const docToSave: TextDocument = {
        ...updatedDocument,
        metadata: {
          ...updatedDocument.metadata,
          size: new Blob([content]).size,
          wordCount,
          readingTime,
          checksum: generateChecksum(content)
        }
      }

      const savedDoc = await documentDAO.update(docToSave.id, docToSave)
      setDocument(savedDoc as TextDocument)

      console.log('文档保存成功:', savedDoc.title)
    } catch (error) {
      console.error('保存文档失败:', error)
      message.error('保存文档失败')
      throw error // 重新抛出错误，让编辑器组件处理
    } finally {
      setSaving(false)
    }
  }, [])

  /**
   * 文档变化处理
   */
  const handleDocumentChange = useCallback((changes: Partial<TextDocument>) => {
    if (document) {
      setDocument(prev => prev ? { ...prev, ...changes } : null)
    }
  }, [document])

  /**
   * 初始化页面
   */
  useEffect(() => {
    if (id) {
      // 加载现有文档
      loadDocument(id)
    } else {
      // 创建新文档
      createNewDocument()
    }
  }, [id, loadDocument, createNewDocument])

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载文档中..." />
      </div>
    )
  }

  if (!document) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-500 mb-4">文档加载失败</div>
          <button
            onClick={() => navigate('/')}
            className="text-blue-500 hover:text-blue-600"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full">
      <TextEditor
        document={document}
        onSave={handleSave}
        onDocumentChange={handleDocumentChange}
        autoSave={true}
        autoSaveDelay={2000}
        height="100%"
      />
    </div>
  )
}

/**
 * 生成内容校验和
 */
function generateChecksum(content: string): string {
  // 简单的校验和算法
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16)
}

export default TextEditorPage
