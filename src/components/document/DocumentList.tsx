/**
 * 文档列表组件
 * 显示文档列表，支持搜索、筛选、排序等功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import { 
  List, 
  Card, 
  Input, 
  Select, 
  Button, 
  Tag, 
  Space, 
  Dropdown, 
  Modal, 
  message,
  Empty,
  Spin
} from 'antd'
import {
  SearchOutlined,
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { BaseDocument, DocumentType, SearchOptions } from '@types/index'
import { documentManager } from '@services/document/documentManager'
import { formatRelativeTime } from '@utils/index'

const { Search } = Input
const { Option } = Select

/**
 * 文档列表组件属性接口
 */
export interface DocumentListProps {
  className?: string
  showCreateButton?: boolean
  showSearch?: boolean
  showFilters?: boolean
  pageSize?: number
  onDocumentSelect?: (document: BaseDocument) => void
  onDocumentCreate?: (type: DocumentType) => void
}

/**
 * 文档列表组件
 */
const DocumentList: React.FC<DocumentListProps> = ({
  className = '',
  showCreateButton = true,
  showSearch = true,
  showFilters = true,
  pageSize = 20,
  onDocumentSelect,
  onDocumentCreate
}) => {
  const navigate = useNavigate()
  
  const [documents, setDocuments] = useState<BaseDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<DocumentType | 'all'>('all')
  const [sortBy, setSortBy] = useState<'updatedAt' | 'createdAt' | 'title'>('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  /**
   * 加载文档列表
   */
  const loadDocuments = useCallback(async () => {
    try {
      setLoading(true)
      
      const searchOptions: SearchOptions = {
        types: selectedType === 'all' ? undefined : [selectedType],
        sortBy,
        sortOrder,
        limit: pageSize
      }

      let results: BaseDocument[]
      if (searchQuery.trim()) {
        results = await documentManager.searchDocuments(searchQuery, searchOptions)
      } else {
        // 获取所有文档并应用筛选和排序
        const allDocs = await documentManager.searchDocuments('', searchOptions)
        results = allDocs
      }

      setDocuments(results)
    } catch (error) {
      console.error('加载文档列表失败:', error)
      message.error('加载文档列表失败')
    } finally {
      setLoading(false)
    }
  }, [searchQuery, selectedType, sortBy, sortOrder, pageSize])

  /**
   * 初始化加载
   */
  useEffect(() => {
    loadDocuments()
  }, [loadDocuments])

  /**
   * 搜索处理
   */
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value)
  }, [])

  /**
   * 创建新文档
   */
  const handleCreateDocument = useCallback((type: DocumentType) => {
    if (onDocumentCreate) {
      onDocumentCreate(type)
    } else {
      // 默认导航到编辑器
      const routes = {
        [DocumentType.TEXT]: '/editor',
        [DocumentType.WHITEBOARD]: '/whiteboard',
        [DocumentType.MINDMAP]: '/mindmap',
        [DocumentType.KANBAN]: '/kanban'
      }
      navigate(routes[type])
    }
  }, [navigate, onDocumentCreate])

  /**
   * 文档点击处理
   */
  const handleDocumentClick = useCallback((document: BaseDocument) => {
    if (onDocumentSelect) {
      onDocumentSelect(document)
    } else {
      // 默认导航到对应编辑器
      const routes = {
        [DocumentType.TEXT]: `/editor/${document.id}`,
        [DocumentType.WHITEBOARD]: `/whiteboard/${document.id}`,
        [DocumentType.MINDMAP]: `/mindmap/${document.id}`,
        [DocumentType.KANBAN]: `/kanban/${document.id}`
      }
      navigate(routes[document.type])
    }
  }, [navigate, onDocumentSelect])

  /**
   * 复制文档
   */
  const handleDuplicateDocument = useCallback(async (document: BaseDocument) => {
    try {
      await documentManager.duplicateDocument(document.id, {
        title: `${document.title} - 副本`
      })
      message.success('文档复制成功')
      loadDocuments()
    } catch (error) {
      console.error('复制文档失败:', error)
      message.error('复制文档失败')
    }
  }, [loadDocuments])

  /**
   * 删除文档
   */
  const handleDeleteDocument = useCallback((document: BaseDocument) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文档"${document.title}"吗？删除后可以在回收站中恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await documentManager.moveToTrash(document.id)
          message.success('文档已移动到回收站')
          loadDocuments()
        } catch (error) {
          console.error('删除文档失败:', error)
          message.error('删除文档失败')
        }
      }
    })
  }, [loadDocuments])

  /**
   * 获取文档类型图标
   */
  const getDocumentIcon = (type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = (type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }

  /**
   * 创建按钮菜单
   */
  const createMenuItems = [
    {
      key: DocumentType.TEXT,
      label: '文本笔记',
      icon: <FileTextOutlined />,
      onClick: () => handleCreateDocument(DocumentType.TEXT)
    },
    {
      key: DocumentType.WHITEBOARD,
      label: '白板',
      icon: <BgColorsOutlined />,
      onClick: () => handleCreateDocument(DocumentType.WHITEBOARD)
    },
    {
      key: DocumentType.MINDMAP,
      label: '思维导图',
      icon: <NodeIndexOutlined />,
      onClick: () => handleCreateDocument(DocumentType.MINDMAP)
    },
    {
      key: DocumentType.KANBAN,
      label: '看板',
      icon: <ProjectOutlined />,
      onClick: () => handleCreateDocument(DocumentType.KANBAN)
    }
  ]

  /**
   * 文档操作菜单
   */
  const getDocumentMenuItems = (document: BaseDocument) => [
    {
      key: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      onClick: () => handleDocumentClick(document)
    },
    {
      key: 'duplicate',
      label: '复制',
      icon: <CopyOutlined />,
      onClick: () => handleDuplicateDocument(document)
    },
    {
      type: 'divider' as const
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => handleDeleteDocument(document)
    }
  ]

  return (
    <div className={`document-list ${className}`}>
      {/* 工具栏 */}
      <div className="document-list-toolbar mb-4 space-y-4">
        {/* 搜索和创建按钮 */}
        <div className="flex items-center justify-between">
          {showSearch && (
            <Search
              placeholder="搜索文档..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              className="flex-1 max-w-md"
              onSearch={handleSearch}
            />
          )}
          
          {showCreateButton && (
            <Dropdown
              menu={{ items: createMenuItems }}
              placement="bottomRight"
            >
              <Button type="primary" size="large" icon={<PlusOutlined />}>
                新建文档
              </Button>
            </Dropdown>
          )}
        </div>

        {/* 筛选和排序 */}
        {showFilters && (
          <div className="flex items-center space-x-4">
            <Select
              value={selectedType}
              onChange={setSelectedType}
              className="w-32"
              placeholder="文档类型"
            >
              <Option value="all">全部类型</Option>
              <Option value={DocumentType.TEXT}>文本笔记</Option>
              <Option value={DocumentType.WHITEBOARD}>白板</Option>
              <Option value={DocumentType.MINDMAP}>思维导图</Option>
              <Option value={DocumentType.KANBAN}>看板</Option>
            </Select>

            <Select
              value={`${sortBy}-${sortOrder}`}
              onChange={(value) => {
                const [field, order] = value.split('-')
                setSortBy(field as any)
                setSortOrder(order as any)
              }}
              className="w-40"
              placeholder="排序方式"
            >
              <Option value="updatedAt-desc">最近修改</Option>
              <Option value="createdAt-desc">最近创建</Option>
              <Option value="title-asc">标题 A-Z</Option>
              <Option value="title-desc">标题 Z-A</Option>
            </Select>
          </div>
        )}
      </div>

      {/* 文档列表 */}
      <div className="document-list-content">
        {loading ? (
          <div className="text-center py-8">
            <Spin size="large" tip="加载中..." />
          </div>
        ) : documents.length === 0 ? (
          <Empty
            description={searchQuery ? '没有找到匹配的文档' : '暂无文档'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            {!searchQuery && showCreateButton && (
              <Dropdown menu={{ items: createMenuItems }}>
                <Button type="primary" icon={<PlusOutlined />}>
                  创建第一个文档
                </Button>
              </Dropdown>
            )}
          </Empty>
        ) : (
          <List
            grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
            dataSource={documents}
            renderItem={(document) => (
              <List.Item>
                <Card
                  hoverable
                  className="document-card h-full"
                  actions={[
                    <Dropdown
                      key="more"
                      menu={{ items: getDocumentMenuItems(document) }}
                      trigger={['click']}
                    >
                      <Button type="text" icon={<MoreOutlined />} />
                    </Dropdown>
                  ]}
                  onClick={() => handleDocumentClick(document)}
                >
                  <Card.Meta
                    avatar={getDocumentIcon(document.type)}
                    title={
                      <div className="flex items-center justify-between">
                        <span className="text-ellipsis flex-1" title={document.title}>
                          {document.title}
                        </span>
                      </div>
                    }
                    description={
                      <div className="space-y-2">
                        <div className="text-xs text-gray-500">
                          {getDocumentTypeName(document.type)}
                        </div>
                        
                        {document.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {document.tags.slice(0, 3).map(tag => (
                              <Tag key={tag} size="small" color="blue">
                                {tag}
                              </Tag>
                            ))}
                            {document.tags.length > 3 && (
                              <Tag size="small">+{document.tags.length - 3}</Tag>
                            )}
                          </div>
                        )}
                        
                        <div className="flex items-center text-xs text-gray-400">
                          <ClockCircleOutlined className="mr-1" />
                          {formatRelativeTime(document.updatedAt)}
                        </div>
                      </div>
                    }
                  />
                </Card>
              </List.Item>
            )}
          />
        )}
      </div>
    </div>
  )
}

export default DocumentList
