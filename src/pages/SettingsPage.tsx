import React from 'react'
import { Card, Typography, Button } from 'antd'
import { SettingOutlined, SaveOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

/**
 * 设置页面组件（占位）
 * 后续将实现完整的设置功能
 */
const SettingsPage: React.FC = () => {
  return (
    <div className="h-full p-6">
      <Card className="h-full">
        <div className="text-center py-20">
          <SettingOutlined className="text-6xl text-gray-500 mb-4" />
          <Title level={2}>应用设置</Title>
          <Paragraph className="text-gray-600 mb-6">
            这里将实现完整的设置功能，包括：
          </Paragraph>
          <div className="text-left max-w-md mx-auto mb-8">
            <ul className="text-gray-600 space-y-2">
              <li>• 主题切换设置</li>
              <li>• 语言偏好设置</li>
              <li>• 编辑器配置</li>
              <li>• 自动保存设置</li>
              <li>• 数据导入导出</li>
              <li>• 快捷键配置</li>
            </ul>
          </div>
          <Button 
            type="primary" 
            size="large"
            icon={<SaveOutlined />}
            disabled
          >
            功能开发中...
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default SettingsPage
