/**
 * 数据库错误处理服务
 * 提供统一的错误处理、日志记录和错误恢复机制
 */

/**
 * 数据库错误类型枚举
 */
export enum DatabaseErrorType {
  CONNECTION_FAILED = 'CONNECTION_FAILED',           // 连接失败
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',         // 事务失败
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',                 // 存储配额超出
  VERSION_ERROR = 'VERSION_ERROR',                   // 版本错误
  CONSTRAINT_ERROR = 'CONSTRAINT_ERROR',             // 约束错误
  NOT_FOUND = 'NOT_FOUND',                          // 数据不存在
  PERMISSION_DENIED = 'PERMISSION_DENIED',           // 权限拒绝
  CORRUPTION = 'CORRUPTION',                         // 数据损坏
  UNKNOWN = 'UNKNOWN'                               // 未知错误
}

/**
 * 数据库错误严重级别
 */
export enum ErrorSeverity {
  LOW = 'low',           // 低级别：不影响核心功能
  MEDIUM = 'medium',     // 中级别：影响部分功能
  HIGH = 'high',         // 高级别：影响核心功能
  CRITICAL = 'critical'  // 严重级别：系统无法正常工作
}

/**
 * 数据库错误接口
 */
export interface DatabaseError {
  type: DatabaseErrorType      // 错误类型
  severity: ErrorSeverity      // 严重级别
  message: string             // 错误消息
  originalError?: Error       // 原始错误对象
  context?: Record<string, any> // 错误上下文
  timestamp: Date             // 错误时间
  stack?: string              // 错误堆栈
  recoverable: boolean        // 是否可恢复
}

/**
 * 错误恢复策略接口
 */
export interface RecoveryStrategy {
  name: string                // 策略名称
  description: string         // 策略描述
  execute: () => Promise<boolean> // 执行恢复操作
  maxRetries: number          // 最大重试次数
  retryDelay: number          // 重试延迟（毫秒）
}

/**
 * 数据库错误处理器类
 */
export class DatabaseErrorHandler {
  private static instance: DatabaseErrorHandler
  private errorLog: DatabaseError[] = []
  private maxLogSize = 1000
  private recoveryStrategies = new Map<DatabaseErrorType, RecoveryStrategy>()

  /**
   * 获取错误处理器实例
   */
  public static getInstance(): DatabaseErrorHandler {
    if (!DatabaseErrorHandler.instance) {
      DatabaseErrorHandler.instance = new DatabaseErrorHandler()
    }
    return DatabaseErrorHandler.instance
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this.initializeRecoveryStrategies()
  }

  /**
   * 处理数据库错误
   * @param error 原始错误
   * @param context 错误上下文
   * @returns 处理后的数据库错误
   */
  public async handleError(
    error: Error | any, 
    context?: Record<string, any>
  ): Promise<DatabaseError> {
    const dbError = this.createDatabaseError(error, context)
    
    // 记录错误日志
    this.logError(dbError)
    
    // 尝试错误恢复
    if (dbError.recoverable) {
      const recovered = await this.attemptRecovery(dbError)
      if (recovered) {
        console.log(`错误恢复成功: ${dbError.type}`)
      }
    }
    
    // 发送错误通知
    this.notifyError(dbError)
    
    return dbError
  }

  /**
   * 创建数据库错误对象
   */
  private createDatabaseError(error: Error | any, context?: Record<string, any>): DatabaseError {
    const errorType = this.classifyError(error)
    const severity = this.determineSeverity(errorType)
    const recoverable = this.isRecoverable(errorType)

    return {
      type: errorType,
      severity,
      message: this.formatErrorMessage(error, errorType),
      originalError: error instanceof Error ? error : undefined,
      context: context || {},
      timestamp: new Date(),
      stack: error?.stack,
      recoverable
    }
  }

  /**
   * 错误分类
   */
  private classifyError(error: any): DatabaseErrorType {
    if (!error) return DatabaseErrorType.UNKNOWN

    const errorName = error.name?.toLowerCase() || ''
    const errorMessage = error.message?.toLowerCase() || ''

    // IndexedDB特定错误
    if (errorName.includes('quotaexceedederror') || errorMessage.includes('quota')) {
      return DatabaseErrorType.QUOTA_EXCEEDED
    }
    
    if (errorName.includes('versionerror') || errorMessage.includes('version')) {
      return DatabaseErrorType.VERSION_ERROR
    }
    
    if (errorName.includes('constrainterror') || errorMessage.includes('constraint')) {
      return DatabaseErrorType.CONSTRAINT_ERROR
    }
    
    if (errorName.includes('notfounderror') || errorMessage.includes('not found')) {
      return DatabaseErrorType.NOT_FOUND
    }
    
    if (errorName.includes('securityerror') || errorMessage.includes('permission')) {
      return DatabaseErrorType.PERMISSION_DENIED
    }
    
    if (errorName.includes('transactioninactiveerror') || errorMessage.includes('transaction')) {
      return DatabaseErrorType.TRANSACTION_FAILED
    }
    
    if (errorMessage.includes('connection') || errorMessage.includes('database')) {
      return DatabaseErrorType.CONNECTION_FAILED
    }
    
    if (errorMessage.includes('corrupt') || errorMessage.includes('damaged')) {
      return DatabaseErrorType.CORRUPTION
    }

    return DatabaseErrorType.UNKNOWN
  }

  /**
   * 确定错误严重级别
   */
  private determineSeverity(errorType: DatabaseErrorType): ErrorSeverity {
    switch (errorType) {
      case DatabaseErrorType.CORRUPTION:
      case DatabaseErrorType.PERMISSION_DENIED:
        return ErrorSeverity.CRITICAL
      
      case DatabaseErrorType.CONNECTION_FAILED:
      case DatabaseErrorType.QUOTA_EXCEEDED:
      case DatabaseErrorType.VERSION_ERROR:
        return ErrorSeverity.HIGH
      
      case DatabaseErrorType.TRANSACTION_FAILED:
      case DatabaseErrorType.CONSTRAINT_ERROR:
        return ErrorSeverity.MEDIUM
      
      case DatabaseErrorType.NOT_FOUND:
      default:
        return ErrorSeverity.LOW
    }
  }

  /**
   * 判断错误是否可恢复
   */
  private isRecoverable(errorType: DatabaseErrorType): boolean {
    switch (errorType) {
      case DatabaseErrorType.CONNECTION_FAILED:
      case DatabaseErrorType.TRANSACTION_FAILED:
      case DatabaseErrorType.NOT_FOUND:
        return true
      
      case DatabaseErrorType.QUOTA_EXCEEDED:
      case DatabaseErrorType.VERSION_ERROR:
      case DatabaseErrorType.CONSTRAINT_ERROR:
        return false
      
      case DatabaseErrorType.CORRUPTION:
      case DatabaseErrorType.PERMISSION_DENIED:
        return false
      
      default:
        return false
    }
  }

  /**
   * 格式化错误消息
   */
  private formatErrorMessage(error: any, errorType: DatabaseErrorType): string {
    const baseMessage = error?.message || '未知错误'
    
    const typeMessages: Record<DatabaseErrorType, string> = {
      [DatabaseErrorType.CONNECTION_FAILED]: '数据库连接失败',
      [DatabaseErrorType.TRANSACTION_FAILED]: '数据库事务失败',
      [DatabaseErrorType.QUOTA_EXCEEDED]: '存储空间不足',
      [DatabaseErrorType.VERSION_ERROR]: '数据库版本错误',
      [DatabaseErrorType.CONSTRAINT_ERROR]: '数据约束违反',
      [DatabaseErrorType.NOT_FOUND]: '数据不存在',
      [DatabaseErrorType.PERMISSION_DENIED]: '权限不足',
      [DatabaseErrorType.CORRUPTION]: '数据库损坏',
      [DatabaseErrorType.UNKNOWN]: '未知数据库错误'
    }

    return `${typeMessages[errorType]}: ${baseMessage}`
  }

  /**
   * 记录错误日志
   */
  private logError(error: DatabaseError): void {
    // 添加到内存日志
    this.errorLog.push(error)
    
    // 保持日志大小限制
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize)
    }
    
    // 控制台输出
    const logLevel = this.getLogLevel(error.severity)
    console[logLevel](`[数据库错误] ${error.type}: ${error.message}`, {
      severity: error.severity,
      timestamp: error.timestamp,
      context: error.context,
      stack: error.stack
    })
    
    // 持久化关键错误
    if (error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH) {
      this.persistError(error)
    }
  }

  /**
   * 获取日志级别
   */
  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error'
      case ErrorSeverity.MEDIUM:
        return 'warn'
      default:
        return 'info'
    }
  }

  /**
   * 持久化错误日志
   */
  private async persistError(error: DatabaseError): Promise<void> {
    try {
      // 将错误保存到localStorage作为备用
      const errorKey = `db_error_${error.timestamp.getTime()}`
      const errorData = {
        type: error.type,
        severity: error.severity,
        message: error.message,
        timestamp: error.timestamp.toISOString(),
        context: error.context
      }
      
      localStorage.setItem(errorKey, JSON.stringify(errorData))
      
      // 清理旧的错误日志（保留最近100个）
      this.cleanupPersistedErrors()
    } catch (e) {
      console.error('持久化错误日志失败:', e)
    }
  }

  /**
   * 清理持久化的错误日志
   */
  private cleanupPersistedErrors(): void {
    try {
      const errorKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('db_error_'))
        .sort()
      
      if (errorKeys.length > 100) {
        const keysToRemove = errorKeys.slice(0, errorKeys.length - 100)
        keysToRemove.forEach(key => localStorage.removeItem(key))
      }
    } catch (e) {
      console.error('清理错误日志失败:', e)
    }
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(error: DatabaseError): Promise<boolean> {
    const strategy = this.recoveryStrategies.get(error.type)
    if (!strategy) {
      return false
    }

    console.log(`尝试错误恢复: ${strategy.name}`)
    
    for (let attempt = 1; attempt <= strategy.maxRetries; attempt++) {
      try {
        const success = await strategy.execute()
        if (success) {
          console.log(`错误恢复成功 (尝试 ${attempt}/${strategy.maxRetries})`)
          return true
        }
      } catch (recoveryError) {
        console.error(`错误恢复失败 (尝试 ${attempt}/${strategy.maxRetries}):`, recoveryError)
      }
      
      if (attempt < strategy.maxRetries) {
        await this.delay(strategy.retryDelay)
      }
    }
    
    console.error(`错误恢复失败，已达到最大重试次数: ${strategy.maxRetries}`)
    return false
  }

  /**
   * 发送错误通知
   */
  private notifyError(error: DatabaseError): void {
    // 发送自定义事件
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('database-error', {
        detail: error
      })
      window.dispatchEvent(event)
    }
    
    // 严重错误的用户通知
    if (error.severity === ErrorSeverity.CRITICAL) {
      this.showUserNotification(error)
    }
  }

  /**
   * 显示用户通知
   */
  private showUserNotification(error: DatabaseError): void {
    // 这里可以集成消息通知组件
    console.error('严重数据库错误，请联系技术支持:', error.message)
  }

  /**
   * 初始化恢复策略
   */
  private initializeRecoveryStrategies(): void {
    // 连接失败恢复策略
    this.recoveryStrategies.set(DatabaseErrorType.CONNECTION_FAILED, {
      name: '重新连接数据库',
      description: '尝试重新建立数据库连接',
      execute: async () => {
        const { dbConnection } = await import('./connection')
        await dbConnection.reconnect()
        return true
      },
      maxRetries: 3,
      retryDelay: 1000
    })

    // 事务失败恢复策略
    this.recoveryStrategies.set(DatabaseErrorType.TRANSACTION_FAILED, {
      name: '重试事务',
      description: '重新执行失败的事务',
      execute: async () => {
        // 这里需要具体的事务重试逻辑
        return false // 暂时返回false，需要具体实现
      },
      maxRetries: 2,
      retryDelay: 500
    })
  }

  /**
   * 获取错误统计
   */
  public getErrorStats(): ErrorStats {
    const stats: ErrorStats = {
      total: this.errorLog.length,
      byType: {},
      bySeverity: {},
      recent: this.errorLog.slice(-10)
    }

    this.errorLog.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1
    })

    return stats
  }

  /**
   * 清空错误日志
   */
  public clearErrorLog(): void {
    this.errorLog = []
    console.log('错误日志已清空')
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * 错误统计接口
 */
export interface ErrorStats {
  total: number
  byType: Record<DatabaseErrorType, number>
  bySeverity: Record<ErrorSeverity, number>
  recent: DatabaseError[]
}

/**
 * 导出错误处理器实例
 */
export const databaseErrorHandler = DatabaseErrorHandler.getInstance()

/**
 * 便捷的错误处理函数
 */
export const handleDatabaseError = (error: Error | any, context?: Record<string, any>) => {
  return databaseErrorHandler.handleError(error, context)
}
