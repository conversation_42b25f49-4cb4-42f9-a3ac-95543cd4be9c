import React from 'react'
import { Card, Typography, Button } from 'antd'
import { NodeIndexOutlined, SaveOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

/**
 * 思维导图页面组件（占位）
 * 后续将实现完整的思维导图功能
 */
const MindmapPage: React.FC = () => {
  return (
    <div className="h-full p-6">
      <Card className="h-full">
        <div className="text-center py-20">
          <NodeIndexOutlined className="text-6xl text-purple-500 mb-4" />
          <Title level={2}>思维导图</Title>
          <Paragraph className="text-gray-600 mb-6">
            这里将实现强大的思维导图功能，包括：
          </Paragraph>
          <div className="text-left max-w-md mx-auto mb-8">
            <ul className="text-gray-600 space-y-2">
              <li>• 节点创建和编辑</li>
              <li>• 分支连接管理</li>
              <li>• 多种布局模式</li>
              <li>• 节点折叠展开</li>
              <li>• 颜色和图标自定义</li>
              <li>• 导出多种格式</li>
            </ul>
          </div>
          <Button 
            type="primary" 
            size="large"
            icon={<SaveOutlined />}
            disabled
          >
            功能开发中...
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default MindmapPage
