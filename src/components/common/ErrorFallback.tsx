import React from 'react'
import { Button, Result } from 'antd'
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'

interface ErrorFallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

/**
 * 错误边界回退组件
 * 当应用发生未捕获错误时显示的友好界面
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary 
}) => {
  const navigate = useNavigate()

  const handleGoHome = () => {
    navigate('/')
    resetErrorBoundary()
  }

  const handleReload = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full">
        <Result
          status="error"
          title="应用出现错误"
          subTitle="抱歉，应用遇到了一个意外错误。您可以尝试刷新页面或返回首页。"
          extra={[
            <Button 
              type="primary" 
              key="reload"
              icon={<ReloadOutlined />}
              onClick={handleReload}
            >
              刷新页面
            </Button>,
            <Button 
              key="home"
              icon={<HomeOutlined />}
              onClick={handleGoHome}
            >
              返回首页
            </Button>,
          ]}
        />
        
        {/* 开发环境下显示错误详情 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-4 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="text-red-800 font-semibold mb-2">错误详情（仅开发环境显示）:</h3>
            <pre className="text-sm text-red-700 whitespace-pre-wrap overflow-auto max-h-40">
              {error.message}
              {error.stack && `\n\n${error.stack}`}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}

export default ErrorFallback
