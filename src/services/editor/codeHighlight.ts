/**
 * 代码高亮服务
 * 基于Prism.js提供代码语法高亮功能
 */

import Prism from 'prismjs'

// 导入常用语言支持
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-typescript'
import 'prismjs/components/prism-jsx'
import 'prismjs/components/prism-tsx'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-scss'
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-markdown'
import 'prismjs/components/prism-python'
import 'prismjs/components/prism-java'
import 'prismjs/components/prism-c'
import 'prismjs/components/prism-cpp'
import 'prismjs/components/prism-csharp'
import 'prismjs/components/prism-php'
import 'prismjs/components/prism-ruby'
import 'prismjs/components/prism-go'
import 'prismjs/components/prism-rust'
import 'prismjs/components/prism-sql'
import 'prismjs/components/prism-bash'
import 'prismjs/components/prism-yaml'
import 'prismjs/components/prism-xml-doc'

// 导入主题样式
import 'prismjs/themes/prism.css'
import 'prismjs/themes/prism-tomorrow.css'

/**
 * 支持的编程语言列表
 */
export const SUPPORTED_LANGUAGES = [
  'javascript', 'js',
  'typescript', 'ts',
  'jsx', 'tsx',
  'html', 'xml',
  'css', 'scss', 'sass',
  'json',
  'markdown', 'md',
  'python', 'py',
  'java',
  'c', 'cpp', 'c++',
  'csharp', 'cs',
  'php',
  'ruby', 'rb',
  'go',
  'rust', 'rs',
  'sql',
  'bash', 'shell', 'sh',
  'yaml', 'yml',
  'text', 'plain'
] as const

export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number]

/**
 * 语言别名映射
 */
const LANGUAGE_ALIASES: Record<string, string> = {
  'js': 'javascript',
  'ts': 'typescript',
  'py': 'python',
  'rb': 'ruby',
  'rs': 'rust',
  'cs': 'csharp',
  'c++': 'cpp',
  'sh': 'bash',
  'shell': 'bash',
  'yml': 'yaml',
  'md': 'markdown',
  'plain': 'text'
}

/**
 * 代码高亮主题
 */
export enum HighlightTheme {
  LIGHT = 'light',
  DARK = 'dark'
}

/**
 * 代码高亮配置接口
 */
export interface HighlightConfig {
  theme: HighlightTheme
  showLineNumbers: boolean
  showLanguage: boolean
  copyButton: boolean
  maxLines?: number
}

/**
 * 代码高亮服务类
 */
export class CodeHighlightService {
  private static instance: CodeHighlightService
  private currentTheme: HighlightTheme = HighlightTheme.LIGHT
  private config: HighlightConfig = {
    theme: HighlightTheme.LIGHT,
    showLineNumbers: true,
    showLanguage: true,
    copyButton: true
  }

  /**
   * 获取服务实例
   */
  public static getInstance(): CodeHighlightService {
    if (!CodeHighlightService.instance) {
      CodeHighlightService.instance = new CodeHighlightService()
    }
    return CodeHighlightService.instance
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this.initializePrism()
  }

  /**
   * 初始化Prism.js
   */
  private initializePrism(): void {
    // 配置Prism.js
    Prism.manual = true // 禁用自动高亮
    
    // 添加行号插件支持
    if (typeof window !== 'undefined') {
      // 动态导入行号插件
      import('prismjs/plugins/line-numbers/prism-line-numbers.js')
        .then(() => import('prismjs/plugins/line-numbers/prism-line-numbers.css'))
        .catch(console.error)
      
      // 动态导入复制按钮插件
      import('prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.js')
        .then(() => import('prismjs/plugins/copy-to-clipboard/prism-copy-to-clipboard.css'))
        .catch(console.error)
    }
  }

  /**
   * 高亮代码
   * @param code 代码字符串
   * @param language 编程语言
   * @returns 高亮后的HTML字符串
   */
  public highlightCode(code: string, language: string = 'text'): string {
    try {
      // 规范化语言名称
      const normalizedLanguage = this.normalizeLanguage(language)
      
      // 检查语言是否支持
      if (!this.isLanguageSupported(normalizedLanguage)) {
        console.warn(`不支持的语言: ${language}，使用纯文本模式`)
        return this.escapeHtml(code)
      }

      // 获取语言语法
      const grammar = Prism.languages[normalizedLanguage]
      if (!grammar) {
        console.warn(`语言语法未加载: ${normalizedLanguage}`)
        return this.escapeHtml(code)
      }

      // 执行高亮
      const highlightedCode = Prism.highlight(code, grammar, normalizedLanguage)
      
      return highlightedCode
    } catch (error) {
      console.error('代码高亮失败:', error)
      return this.escapeHtml(code)
    }
  }

  /**
   * 高亮代码块并包装为完整的HTML结构
   * @param code 代码字符串
   * @param language 编程语言
   * @param options 高亮选项
   * @returns 完整的HTML代码块
   */
  public highlightCodeBlock(
    code: string, 
    language: string = 'text',
    options: Partial<HighlightConfig> = {}
  ): string {
    const config = { ...this.config, ...options }
    const normalizedLanguage = this.normalizeLanguage(language)
    const highlightedCode = this.highlightCode(code, normalizedLanguage)
    
    // 构建CSS类名
    const cssClasses = [
      'code-block',
      `language-${normalizedLanguage}`,
      config.theme === HighlightTheme.DARK ? 'dark-theme' : 'light-theme'
    ]
    
    if (config.showLineNumbers) {
      cssClasses.push('line-numbers')
    }

    // 构建HTML结构
    let html = `<div class="code-block-container">`
    
    // 添加语言标签
    if (config.showLanguage && normalizedLanguage !== 'text') {
      html += `<div class="code-language-label">${this.getLanguageDisplayName(normalizedLanguage)}</div>`
    }
    
    // 添加复制按钮
    if (config.copyButton) {
      html += `<button class="code-copy-button" data-code="${this.escapeHtml(code)}" title="复制代码">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
      </button>`
    }
    
    // 添加代码内容
    html += `<pre class="${cssClasses.join(' ')}"><code class="language-${normalizedLanguage}">${highlightedCode}</code></pre>`
    
    html += `</div>`
    
    return html
  }

  /**
   * 高亮页面中的所有代码块
   * @param container 容器元素
   */
  public highlightAllInContainer(container: HTMLElement): void {
    const codeBlocks = container.querySelectorAll('pre code[class*="language-"]')
    
    codeBlocks.forEach((block) => {
      const codeElement = block as HTMLElement
      const language = this.extractLanguageFromClassName(codeElement.className)
      const code = codeElement.textContent || ''
      
      if (code.trim()) {
        const highlightedCode = this.highlightCode(code, language)
        codeElement.innerHTML = highlightedCode
      }
    })
    
    // 添加复制功能
    this.addCopyFunctionality(container)
  }

  /**
   * 设置高亮主题
   * @param theme 主题
   */
  public setTheme(theme: HighlightTheme): void {
    this.currentTheme = theme
    this.config.theme = theme
    
    // 更新页面中的主题类
    if (typeof document !== 'undefined') {
      const codeBlocks = document.querySelectorAll('.code-block-container')
      codeBlocks.forEach(block => {
        block.classList.remove('light-theme', 'dark-theme')
        block.classList.add(theme === HighlightTheme.DARK ? 'dark-theme' : 'light-theme')
      })
    }
  }

  /**
   * 更新配置
   * @param newConfig 新配置
   */
  public updateConfig(newConfig: Partial<HighlightConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取当前配置
   */
  public getConfig(): HighlightConfig {
    return { ...this.config }
  }

  /**
   * 规范化语言名称
   */
  private normalizeLanguage(language: string): string {
    const normalized = language.toLowerCase().trim()
    return LANGUAGE_ALIASES[normalized] || normalized
  }

  /**
   * 检查语言是否支持
   */
  private isLanguageSupported(language: string): boolean {
    return SUPPORTED_LANGUAGES.includes(language as SupportedLanguage) || 
           Prism.languages[language] !== undefined
  }

  /**
   * 获取语言显示名称
   */
  private getLanguageDisplayName(language: string): string {
    const displayNames: Record<string, string> = {
      'javascript': 'JavaScript',
      'typescript': 'TypeScript',
      'jsx': 'JSX',
      'tsx': 'TSX',
      'html': 'HTML',
      'css': 'CSS',
      'scss': 'SCSS',
      'json': 'JSON',
      'markdown': 'Markdown',
      'python': 'Python',
      'java': 'Java',
      'c': 'C',
      'cpp': 'C++',
      'csharp': 'C#',
      'php': 'PHP',
      'ruby': 'Ruby',
      'go': 'Go',
      'rust': 'Rust',
      'sql': 'SQL',
      'bash': 'Bash',
      'yaml': 'YAML',
      'xml': 'XML'
    }
    
    return displayNames[language] || language.toUpperCase()
  }

  /**
   * 从类名中提取语言
   */
  private extractLanguageFromClassName(className: string): string {
    const match = className.match(/language-(\w+)/)
    return match ? match[1] : 'text'
  }

  /**
   * 转义HTML字符
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 添加复制功能
   */
  private addCopyFunctionality(container: HTMLElement): void {
    const copyButtons = container.querySelectorAll('.code-copy-button')
    
    copyButtons.forEach(button => {
      button.addEventListener('click', async (event) => {
        const target = event.target as HTMLElement
        const code = target.getAttribute('data-code') || ''
        
        try {
          await navigator.clipboard.writeText(code)
          
          // 显示复制成功反馈
          const originalTitle = target.getAttribute('title')
          target.setAttribute('title', '已复制!')
          target.classList.add('copied')
          
          setTimeout(() => {
            target.setAttribute('title', originalTitle || '复制代码')
            target.classList.remove('copied')
          }, 2000)
        } catch (error) {
          console.error('复制失败:', error)
        }
      })
    })
  }
}

/**
 * 导出服务实例
 */
export const codeHighlightService = CodeHighlightService.getInstance()

/**
 * 便捷函数
 */
export const highlightCode = (code: string, language: string = 'text') => {
  return codeHighlightService.highlightCode(code, language)
}

export const highlightCodeBlock = (
  code: string, 
  language: string = 'text',
  options: Partial<HighlightConfig> = {}
) => {
  return codeHighlightService.highlightCodeBlock(code, language, options)
}
