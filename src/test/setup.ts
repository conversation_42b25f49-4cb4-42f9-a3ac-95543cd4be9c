import { expect, afterEach } from 'vitest'
import { cleanup } from '@testing-library/react'
import * as matchers from '@testing-library/jest-dom/matchers'

// 扩展expect匹配器
expect.extend(matchers)

// 每个测试后清理DOM
afterEach(() => {
  cleanup()
})

// 模拟浏览器API
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
})

// 模拟ResizeObserver
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

// 模拟IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
}

// 模拟IndexedDB
const mockIDBRequest = {
  result: null,
  error: null,
  onsuccess: null,
  onerror: null,
}

const mockIDBDatabase = {
  createObjectStore: () => ({}),
  transaction: () => ({
    objectStore: () => ({
      add: () => mockIDBRequest,
      get: () => mockIDBRequest,
      put: () => mockIDBRequest,
      delete: () => mockIDBRequest,
      getAll: () => mockIDBRequest,
    }),
  }),
}

global.indexedDB = {
  open: () => ({
    ...mockIDBRequest,
    result: mockIDBDatabase,
  }),
  deleteDatabase: () => mockIDBRequest,
} as any

// 模拟console方法以避免测试输出污染
global.console = {
  ...console,
  // 保留error和warn用于调试
  log: () => {},
  debug: () => {},
  info: () => {},
}
