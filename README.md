# 多维度笔记应用

现代化的多维度笔记应用，支持文本、白板、思维导图、看板等多种格式，并提供强大的关联系统帮助您构建个人知识网络。

## 🚀 功能特性

### 核心功能
- **📝 文本笔记**: 支持Markdown格式，实时预览，代码高亮，数学公式
- **🎨 白板绘图**: 自由绘制，图形标注，无限画布，多层管理
- **🧠 思维导图**: 结构化思维整理，多种布局，丰富样式
- **📋 看板管理**: 任务管理，拖拽操作，进度跟踪

### 关联系统
- **🔗 双向链接**: 文档间相互引用，自动反向链接
- **🕸️ 关系图谱**: 可视化知识网络，社区检测分析
- **🔄 跨格式关联**: 不同类型文档间的无缝关联

### 技术特性
- **📱 响应式设计**: 支持桌面、平板、移动端
- **🌙 主题切换**: 明暗模式自由切换
- **💾 本地存储**: 数据本地化，保护隐私
- **📤 导入导出**: 支持多种格式，数据可移植
- **🔍 全文搜索**: 快速查找，智能建议

## 🛠️ 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件**: Ant Design + Tailwind CSS
- **状态管理**: Zustand + React Query
- **编辑器**: Monaco Editor
- **绘图引擎**: Fabric.js + D3.js
- **数据存储**: IndexedDB
- **PWA支持**: Service Worker + Workbox

## 📦 安装和运行

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发模式
```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 运行测试
```bash
npm run test
# 或
yarn test
```

## 📁 项目结构

```
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   ├── layout/         # 布局组件
│   └── ...
├── pages/              # 页面组件
├── hooks/              # 自定义Hook
├── services/           # 业务服务
├── stores/             # 状态管理
├── types/              # 类型定义
├── utils/              # 工具函数
├── assets/             # 静态资源
└── test/               # 测试配置
```

## 🗺️ 开发路线图

### MVP版本 (v1.0) - 已完成基础框架
- [x] 项目初始化和基础架构
- [x] 响应式布局和导航
- [x] 类型定义和数据模型
- [ ] 文本编辑器功能
- [ ] 关联系统基础功能
- [ ] 搜索和数据管理

### 增强版本 (v1.1-v1.3)
- [ ] 白板绘图功能
- [ ] 思维导图功能
- [ ] 看板管理功能
- [ ] 跨格式关联系统

### 专业版本 (v2.0+)
- [ ] 高级搜索和分析
- [ ] 协作功能
- [ ] 插件系统
- [ ] AI增强功能

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-org/multidimensional-notes/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有开源项目的贡献者，特别是：
- React团队
- Ant Design团队
- Vite团队
- 以及所有依赖库的维护者

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！

