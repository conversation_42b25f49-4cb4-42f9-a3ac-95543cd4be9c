<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="现代化的多维度笔记应用，支持文本、白板、思维导图、看板等多种格式" />
    <meta name="keywords" content="笔记,markdown,白板,思维导图,看板,知识管理" />
    <meta name="author" content="Developer Team" />
    
    <!-- PWA相关 -->
    <meta name="theme-color" content="#1890ff" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <title>多维度笔记应用</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
