@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    background-color: #ffffff;
    color: #262626;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 选择文本样式 */
  ::selection {
    background-color: #1890ff;
    color: white;
  }

  ::-moz-selection {
    background-color: #1890ff;
    color: white;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮组件样式 */
  .btn-primary {
    @apply bg-primary-500 text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors duration-200;
  }

  /* 卡片组件样式 */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200 p-4;
  }

  .card-hover {
    @apply hover:shadow-medium transition-shadow duration-200;
  }

  /* 输入框样式 */
  .input-primary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  /* 布局样式 */
  .layout-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .sidebar {
    @apply w-64 bg-gray-50 border-r border-gray-200 flex-shrink-0;
  }

  .main-content {
    @apply flex-1 overflow-hidden;
  }
}

/* 工具类样式 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis {
    @apply truncate;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 居中布局 */
  .center {
    @apply flex items-center justify-center;
  }

  .center-x {
    @apply flex justify-center;
  }

  .center-y {
    @apply flex items-center;
  }

  /* 间距工具 */
  .space-between {
    @apply flex items-center justify-between;
  }

  /* 隐藏滚动条 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 渐变背景 */
  .gradient-primary {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f0f2f5 0%, #ffffff 100%);
  }
}

/* 暗色主题 */
.dark {
  color-scheme: dark;
}

.dark body {
  @apply bg-gray-900 text-gray-100;
}

.dark .card {
  @apply bg-gray-800 border-gray-700;
}

.dark .sidebar {
  @apply bg-gray-800 border-gray-700;
}

.dark .input-primary {
  @apply bg-gray-800 border-gray-600 text-gray-100;
}

.dark .input-primary:focus {
  @apply ring-primary-400 border-transparent;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    @apply w-full;
  }
  
  .layout-container {
    @apply px-2;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  .card {
    @apply shadow-none border border-gray-300;
  }
}
