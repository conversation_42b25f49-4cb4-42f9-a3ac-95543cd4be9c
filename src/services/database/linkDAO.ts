/**
 * 链接数据访问对象 (Link DAO)
 * 提供文档链接关系的创建、读取、更新、删除等数据库操作
 */

import { getDB } from './connection'
import { DocumentLink, LinkType } from '@types/index'
import { generateUUID } from '@utils/index'

/**
 * 链接数据访问对象类
 * 封装所有与文档链接相关的数据库操作
 */
export class LinkDAO {
  /**
   * 创建新链接
   * @param link 链接数据（不包含ID，会自动生成）
   * @returns Promise<DocumentLink> 创建的链接（包含生成的ID）
   */
  async create(link: Omit<DocumentLink, 'id' | 'createdAt' | 'updatedAt'>): Promise<DocumentLink> {
    try {
      const db = await getDB()
      
      // 生成链接ID和时间戳
      const now = new Date()
      const newLink: DocumentLink = {
        ...link,
        id: generateUUID(),
        createdAt: now,
        updatedAt: now,
        metadata: link.metadata || {}
      }

      // 验证链接数据
      this.validateLink(newLink)

      // 检查是否已存在相同的链接
      const existingLink = await this.findExisting(newLink.sourceId, newLink.targetId, newLink.type)
      if (existingLink) {
        console.warn(`链接已存在: ${newLink.sourceId} -> ${newLink.targetId} (${newLink.type})`)
        return existingLink
      }

      // 保存到数据库
      await db.add('links', newLink)
      
      console.log(`链接创建成功: ${newLink.sourceId} -> ${newLink.targetId} (${newLink.type})`)
      return newLink
    } catch (error) {
      console.error('创建链接失败:', error)
      throw new Error(`创建链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据ID获取链接
   * @param id 链接ID
   * @returns Promise<DocumentLink | undefined> 链接数据或undefined
   */
  async getById(id: string): Promise<DocumentLink | undefined> {
    try {
      const db = await getDB()
      const link = await db.get('links', id)
      
      if (link) {
        console.log(`获取链接成功: ${id}`)
      } else {
        console.warn(`链接不存在: ${id}`)
      }
      
      return link
    } catch (error) {
      console.error('获取链接失败:', error)
      throw new Error(`获取链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取文档的所有出链（该文档指向其他文档的链接）
   * @param documentId 文档ID
   * @returns Promise<DocumentLink[]> 出链列表
   */
  async getOutgoingLinks(documentId: string): Promise<DocumentLink[]> {
    try {
      const db = await getDB()
      const links = await db.getAllFromIndex('links', 'by-source', documentId)
      
      console.log(`获取文档 ${documentId} 的出链成功: ${links.length} 个链接`)
      return links
    } catch (error) {
      console.error('获取出链失败:', error)
      throw new Error(`获取出链失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取文档的所有入链（其他文档指向该文档的链接）
   * @param documentId 文档ID
   * @returns Promise<DocumentLink[]> 入链列表
   */
  async getIncomingLinks(documentId: string): Promise<DocumentLink[]> {
    try {
      const db = await getDB()
      const links = await db.getAllFromIndex('links', 'by-target', documentId)
      
      console.log(`获取文档 ${documentId} 的入链成功: ${links.length} 个链接`)
      return links
    } catch (error) {
      console.error('获取入链失败:', error)
      throw new Error(`获取入链失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取文档的所有链接（包括出链和入链）
   * @param documentId 文档ID
   * @returns Promise<{outgoing: DocumentLink[], incoming: DocumentLink[]}> 链接数据
   */
  async getDocumentLinks(documentId: string): Promise<{
    outgoing: DocumentLink[]
    incoming: DocumentLink[]
  }> {
    try {
      const [outgoing, incoming] = await Promise.all([
        this.getOutgoingLinks(documentId),
        this.getIncomingLinks(documentId)
      ])
      
      console.log(`获取文档 ${documentId} 的所有链接成功: ${outgoing.length} 个出链, ${incoming.length} 个入链`)
      return { outgoing, incoming }
    } catch (error) {
      console.error('获取文档链接失败:', error)
      throw new Error(`获取文档链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据类型获取链接
   * @param type 链接类型
   * @returns Promise<DocumentLink[]> 链接列表
   */
  async getByType(type: LinkType): Promise<DocumentLink[]> {
    try {
      const db = await getDB()
      const links = await db.getAllFromIndex('links', 'by-type', type)
      
      console.log(`获取${type}类型链接成功: ${links.length} 个链接`)
      return links
    } catch (error) {
      console.error('根据类型获取链接失败:', error)
      throw new Error(`根据类型获取链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 更新链接
   * @param id 链接ID
   * @param updates 要更新的字段
   * @returns Promise<DocumentLink> 更新后的链接
   */
  async update(id: string, updates: Partial<DocumentLink>): Promise<DocumentLink> {
    try {
      const db = await getDB()
      
      // 获取现有链接
      const existingLink = await this.getById(id)
      if (!existingLink) {
        throw new Error(`链接不存在: ${id}`)
      }

      // 合并更新数据
      const updatedLink: DocumentLink = {
        ...existingLink,
        ...updates,
        id, // 确保ID不被修改
        createdAt: existingLink.createdAt, // 确保创建时间不被修改
        updatedAt: new Date() // 更新修改时间
      }

      // 验证更新后的链接数据
      this.validateLink(updatedLink)

      // 保存到数据库
      await db.put('links', updatedLink)
      
      console.log(`链接更新成功: ${id}`)
      return updatedLink
    } catch (error) {
      console.error('更新链接失败:', error)
      throw new Error(`更新链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除链接
   * @param id 链接ID
   * @returns Promise<void>
   */
  async delete(id: string): Promise<void> {
    try {
      const db = await getDB()
      await db.delete('links', id)
      
      console.log(`链接删除成功: ${id}`)
    } catch (error) {
      console.error('删除链接失败:', error)
      throw new Error(`删除链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除文档的所有链接
   * @param documentId 文档ID
   * @returns Promise<void>
   */
  async deleteDocumentLinks(documentId: string): Promise<void> {
    try {
      const { outgoing, incoming } = await this.getDocumentLinks(documentId)
      const allLinks = [...outgoing, ...incoming]
      
      // 批量删除链接
      const db = await getDB()
      const tx = db.transaction('links', 'readwrite')
      
      await Promise.all(
        allLinks.map(link => tx.store.delete(link.id))
      )
      
      await tx.done
      
      console.log(`删除文档 ${documentId} 的所有链接成功: ${allLinks.length} 个链接`)
    } catch (error) {
      console.error('删除文档链接失败:', error)
      throw new Error(`删除文档链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取所有链接
   * @returns Promise<DocumentLink[]> 所有链接列表
   */
  async getAll(): Promise<DocumentLink[]> {
    try {
      const db = await getDB()
      const links = await db.getAll('links')
      
      console.log(`获取所有链接成功: ${links.length} 个链接`)
      return links
    } catch (error) {
      console.error('获取所有链接失败:', error)
      throw new Error(`获取所有链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 查找孤立文档（没有任何链接的文档）
   * @param documentIds 要检查的文档ID列表
   * @returns Promise<string[]> 孤立文档ID列表
   */
  async findOrphanDocuments(documentIds: string[]): Promise<string[]> {
    try {
      const orphans: string[] = []
      
      for (const documentId of documentIds) {
        const { outgoing, incoming } = await this.getDocumentLinks(documentId)
        if (outgoing.length === 0 && incoming.length === 0) {
          orphans.push(documentId)
        }
      }
      
      console.log(`找到孤立文档: ${orphans.length} 个`)
      return orphans
    } catch (error) {
      console.error('查找孤立文档失败:', error)
      throw new Error(`查找孤立文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 查找现有链接
   * @param sourceId 源文档ID
   * @param targetId 目标文档ID
   * @param type 链接类型
   * @returns Promise<DocumentLink | undefined> 现有链接或undefined
   */
  private async findExisting(
    sourceId: string, 
    targetId: string, 
    type: LinkType
  ): Promise<DocumentLink | undefined> {
    try {
      const outgoingLinks = await this.getOutgoingLinks(sourceId)
      return outgoingLinks.find(link => 
        link.targetId === targetId && link.type === type
      )
    } catch (error) {
      console.error('查找现有链接失败:', error)
      return undefined
    }
  }

  /**
   * 验证链接数据
   * @param link 链接数据
   */
  private validateLink(link: DocumentLink): void {
    if (!link.id) {
      throw new Error('链接ID不能为空')
    }
    
    if (!link.sourceId) {
      throw new Error('源文档ID不能为空')
    }
    
    if (!link.targetId) {
      throw new Error('目标文档ID不能为空')
    }
    
    if (link.sourceId === link.targetId) {
      throw new Error('源文档和目标文档不能相同')
    }
    
    if (!Object.values(LinkType).includes(link.type)) {
      throw new Error(`无效的链接类型: ${link.type}`)
    }
    
    if (!link.createdAt || !link.updatedAt) {
      throw new Error('链接创建时间和更新时间不能为空')
    }
  }
}

/**
 * 导出链接DAO实例
 */
export const linkDAO = new LinkDAO()
