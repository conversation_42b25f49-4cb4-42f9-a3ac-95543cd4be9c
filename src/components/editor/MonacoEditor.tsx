/**
 * Monaco编辑器组件
 * 基于Monaco Editor的Markdown编辑器，支持语法高亮、自动完成等功能
 */

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { Editor, OnMount, OnChange } from '@monaco-editor/react'
import { editor } from 'monaco-editor'
import { Spin } from 'antd'
import { debounce } from '@utils/index'

/**
 * Monaco编辑器组件属性接口
 */
export interface MonacoEditorProps {
  value: string // 编辑器内容
  onChange?: (value: string) => void // 内容变化回调
  onSave?: (value: string) => void // 保存回调
  language?: string // 编程语言
  theme?: 'light' | 'dark' // 主题
  readOnly?: boolean // 是否只读
  height?: string | number // 编辑器高度
  width?: string | number // 编辑器宽度
  options?: editor.IStandaloneEditorConstructionOptions // Monaco编辑器选项
  className?: string // CSS类名
  placeholder?: string // 占位符文本
  autoSave?: boolean // 是否自动保存
  autoSaveDelay?: number // 自动保存延迟（毫秒）
}

/**
 * Monaco编辑器组件
 * 提供强大的代码编辑功能，特别优化了Markdown编辑体验
 */
const MonacoEditor: React.FC<MonacoEditorProps> = ({
  value,
  onChange,
  onSave,
  language = 'markdown',
  theme = 'light',
  readOnly = false,
  height = '100%',
  width = '100%',
  options = {},
  className = '',
  placeholder = '开始编写您的内容...',
  autoSave = true,
  autoSaveDelay = 2000
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [currentValue, setCurrentValue] = useState(value)

  /**
   * 防抖的自动保存函数
   */
  const debouncedAutoSave = useCallback(
    debounce((content: string) => {
      if (autoSave && onSave && content !== value) {
        console.log('自动保存文档内容')
        onSave(content)
      }
    }, autoSaveDelay),
    [autoSave, onSave, value, autoSaveDelay]
  )

  /**
   * 编辑器挂载回调
   */
  const handleEditorDidMount: OnMount = useCallback((editor, monaco) => {
    editorRef.current = editor
    setIsLoading(false)

    // 配置Markdown语言特性
    configureMarkdownLanguage(monaco)

    // 设置编辑器主题
    configureEditorTheme(monaco, theme)

    // 注册快捷键
    registerKeyboardShortcuts(editor)

    // 设置占位符
    if (placeholder && !value) {
      setPlaceholder(editor, placeholder)
    }

    // 聚焦编辑器
    editor.focus()

    console.log('Monaco编辑器初始化完成')
  }, [theme, placeholder, value])

  /**
   * 内容变化回调
   */
  const handleEditorChange: OnChange = useCallback((newValue) => {
    const content = newValue || ''
    setCurrentValue(content)
    
    // 触发onChange回调
    if (onChange) {
      onChange(content)
    }

    // 触发自动保存
    if (autoSave) {
      debouncedAutoSave(content)
    }
  }, [onChange, autoSave, debouncedAutoSave])

  /**
   * 手动保存
   */
  const handleSave = useCallback(() => {
    if (onSave && editorRef.current) {
      const content = editorRef.current.getValue()
      console.log('手动保存文档内容')
      onSave(content)
    }
  }, [onSave])

  /**
   * 获取编辑器实例
   */
  const getEditor = useCallback(() => {
    return editorRef.current
  }, [])

  /**
   * 设置编辑器内容
   */
  const setValue = useCallback((newValue: string) => {
    if (editorRef.current) {
      editorRef.current.setValue(newValue)
    }
  }, [])

  /**
   * 获取编辑器内容
   */
  const getValue = useCallback(() => {
    return editorRef.current?.getValue() || ''
  }, [])

  /**
   * 插入文本
   */
  const insertText = useCallback((text: string) => {
    if (editorRef.current) {
      const selection = editorRef.current.getSelection()
      const range = selection || new monaco.Range(1, 1, 1, 1)
      
      editorRef.current.executeEdits('insert-text', [{
        range,
        text,
        forceMoveMarkers: true
      }])
      
      editorRef.current.focus()
    }
  }, [])

  // 当外部value变化时更新编辑器内容
  useEffect(() => {
    if (value !== currentValue && editorRef.current) {
      editorRef.current.setValue(value)
      setCurrentValue(value)
    }
  }, [value, currentValue])

  // 默认编辑器选项
  const defaultOptions: editor.IStandaloneEditorConstructionOptions = {
    minimap: { enabled: false }, // 禁用小地图
    wordWrap: 'on', // 自动换行
    lineNumbers: 'on', // 显示行号
    fontSize: 14, // 字体大小
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace', // 字体
    tabSize: 2, // Tab大小
    insertSpaces: true, // 使用空格代替Tab
    automaticLayout: true, // 自动布局
    scrollBeyondLastLine: false, // 不允许滚动到最后一行之后
    renderWhitespace: 'selection', // 显示空白字符
    bracketPairColorization: { enabled: true }, // 括号配对着色
    guides: {
      bracketPairs: true, // 显示括号配对指南
      indentation: true // 显示缩进指南
    },
    suggest: {
      showKeywords: true, // 显示关键字建议
      showSnippets: true // 显示代码片段建议
    },
    readOnly,
    ...options
  }

  return (
    <div className={`monaco-editor-container ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-full">
          <Spin size="large" tip="编辑器加载中..." />
        </div>
      )}
      
      <Editor
        height={height}
        width={width}
        language={language}
        theme={theme === 'dark' ? 'vs-dark' : 'vs'}
        value={currentValue}
        options={defaultOptions}
        onMount={handleEditorDidMount}
        onChange={handleEditorChange}
        loading={<Spin size="large" tip="编辑器加载中..." />}
      />
    </div>
  )
}

/**
 * 配置Markdown语言特性
 */
function configureMarkdownLanguage(monaco: any) {
  // 注册Markdown语言的自动完成提供者
  monaco.languages.registerCompletionItemProvider('markdown', {
    provideCompletionItems: (model: any, position: any) => {
      const suggestions = [
        {
          label: 'h1',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '# ${1:标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '一级标题'
        },
        {
          label: 'h2',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '## ${1:标题}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '二级标题'
        },
        {
          label: 'bold',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '**${1:粗体文本}**',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '粗体文本'
        },
        {
          label: 'italic',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '*${1:斜体文本}*',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '斜体文本'
        },
        {
          label: 'code',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '`${1:代码}`',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '行内代码'
        },
        {
          label: 'codeblock',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '```${1:language}\n${2:代码内容}\n```',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '代码块'
        },
        {
          label: 'link',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '[${1:链接文本}](${2:URL})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '链接'
        },
        {
          label: 'image',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '![${1:图片描述}](${2:图片URL})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: '图片'
        }
      ]
      
      return { suggestions }
    }
  })
}

/**
 * 配置编辑器主题
 */
function configureEditorTheme(monaco: any, theme: 'light' | 'dark') {
  // 可以在这里自定义主题
  if (theme === 'dark') {
    monaco.editor.setTheme('vs-dark')
  } else {
    monaco.editor.setTheme('vs')
  }
}

/**
 * 注册键盘快捷键
 */
function registerKeyboardShortcuts(editor: editor.IStandaloneCodeEditor) {
  // Ctrl+S 保存
  editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
    // 触发保存事件
    const event = new CustomEvent('editor-save', {
      detail: { content: editor.getValue() }
    })
    window.dispatchEvent(event)
  })

  // Ctrl+B 粗体
  editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyB, () => {
    insertMarkdownFormat(editor, '**', '**', '粗体文本')
  })

  // Ctrl+I 斜体
  editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyI, () => {
    insertMarkdownFormat(editor, '*', '*', '斜体文本')
  })

  // Ctrl+K 链接
  editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyK, () => {
    insertMarkdownFormat(editor, '[', '](URL)', '链接文本')
  })
}

/**
 * 插入Markdown格式
 */
function insertMarkdownFormat(
  editor: editor.IStandaloneCodeEditor,
  prefix: string,
  suffix: string,
  placeholder: string
) {
  const selection = editor.getSelection()
  if (!selection) return

  const selectedText = editor.getModel()?.getValueInRange(selection) || placeholder
  const newText = `${prefix}${selectedText}${suffix}`

  editor.executeEdits('format', [{
    range: selection,
    text: newText,
    forceMoveMarkers: true
  }])

  // 选中插入的文本（不包括格式符号）
  const newSelection = new monaco.Range(
    selection.startLineNumber,
    selection.startColumn + prefix.length,
    selection.endLineNumber,
    selection.endColumn + prefix.length + (selectedText === placeholder ? placeholder.length : 0)
  )
  editor.setSelection(newSelection)
  editor.focus()
}

/**
 * 设置占位符
 */
function setPlaceholder(editor: editor.IStandaloneCodeEditor, placeholder: string) {
  // Monaco Editor没有内置的占位符功能，这里可以通过装饰器实现
  // 暂时省略实现，可以后续添加
}

export default MonacoEditor

// 导出编辑器相关的工具函数
export {
  insertMarkdownFormat,
  configureMarkdownLanguage,
  configureEditorTheme,
  registerKeyboardShortcuts
}
