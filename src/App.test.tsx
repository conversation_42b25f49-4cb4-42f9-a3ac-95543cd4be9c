import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ConfigProvider } from 'antd'
import App from './App'

// 创建测试用的QueryClient
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
})

// 测试包装器组件
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient()
  
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider>
          {children}
        </ConfigProvider>
      </QueryClientProvider>
    </BrowserRouter>
  )
}

describe('App组件', () => {
  it('应用能够正常渲染', () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )
    
    // 验证应用是否正常渲染
    // 由于使用了懒加载，这里主要验证布局结构
    expect(document.body).toBeTruthy()
  })

  it('应该包含正确的文档标题', () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    )
    
    // 验证页面标题
    expect(document.title).toBe('多维度笔记应用')
  })
})
