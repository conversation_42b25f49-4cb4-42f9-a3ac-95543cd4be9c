/**
 * Markdown预览组件
 * 将Markdown文本渲染为HTML，支持代码高亮、数学公式等扩展功能
 */

import React, { useMemo, useEffect, useRef } from 'react'
import { marked } from 'marked'
import { Spin } from 'antd'
import { codeHighlightService } from '@services/editor/codeHighlight'
import { mathRendererService } from '@services/editor/mathRenderer'

/**
 * Markdown预览组件属性接口
 */
export interface MarkdownPreviewProps {
  content: string // Markdown内容
  className?: string // CSS类名
  loading?: boolean // 是否显示加载状态
  onLinkClick?: (url: string, event: React.MouseEvent) => void // 链接点击回调
  enableMath?: boolean // 是否启用数学公式
  enableCodeHighlight?: boolean // 是否启用代码高亮
  enableToc?: boolean // 是否启用目录
}

/**
 * Markdown预览组件
 * 提供实时的Markdown渲染预览功能
 */
const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({
  content,
  className = '',
  loading = false,
  onLinkClick,
  enableMath = true,
  enableCodeHighlight = true,
  enableToc = false
}) => {
  const previewRef = useRef<HTMLDivElement>(null)

  /**
   * 配置marked选项
   */
  const markedOptions = useMemo(() => {
    // 配置marked渲染器
    const renderer = new marked.Renderer()

    // 自定义链接渲染
    renderer.link = (href, title, text) => {
      const titleAttr = title ? ` title="${title}"` : ''
      return `<a href="${href}"${titleAttr} class="markdown-link" data-href="${href}">${text}</a>`
    }

    // 自定义代码块渲染
    renderer.code = (code, language) => {
      const lang = language || 'text'
      return `<pre class="code-block"><code class="language-${lang}" data-language="${lang}">${escapeHtml(code)}</code></pre>`
    }

    // 自定义行内代码渲染
    renderer.codespan = (code) => {
      return `<code class="inline-code">${escapeHtml(code)}</code>`
    }

    // 自定义标题渲染（用于目录生成）
    renderer.heading = (text, level) => {
      const id = generateHeadingId(text)
      return `<h${level} id="${id}" class="markdown-heading heading-${level}">${text}</h${level}>`
    }

    // 自定义表格渲染
    renderer.table = (header, body) => {
      return `<div class="table-wrapper"><table class="markdown-table"><thead>${header}</thead><tbody>${body}</tbody></table></div>`
    }

    // 自定义引用块渲染
    renderer.blockquote = (quote) => {
      return `<blockquote class="markdown-blockquote">${quote}</blockquote>`
    }

    // 自定义列表渲染
    renderer.list = (body, ordered, start) => {
      const tag = ordered ? 'ol' : 'ul'
      const startAttr = ordered && start !== 1 ? ` start="${start}"` : ''
      return `<${tag}${startAttr} class="markdown-list ${ordered ? 'ordered-list' : 'unordered-list'}">${body}</${tag}>`
    }

    // 自定义图片渲染
    renderer.image = (href, title, text) => {
      const titleAttr = title ? ` title="${title}"` : ''
      const altAttr = text ? ` alt="${text}"` : ''
      return `<img src="${href}"${altAttr}${titleAttr} class="markdown-image" loading="lazy" />`
    }

    return {
      renderer,
      gfm: true, // 启用GitHub风格Markdown
      breaks: true, // 启用换行符转换
      pedantic: false,
      sanitize: false, // 不清理HTML（我们会手动处理）
      smartLists: true,
      smartypants: true // 启用智能标点
    }
  }, [])

  /**
   * 渲染Markdown为HTML
   */
  const renderedHtml = useMemo(() => {
    if (!content.trim()) {
      return '<div class="empty-content">暂无内容</div>'
    }

    try {
      // 预处理内容（处理数学公式等）
      let processedContent = content

      // 处理数学公式（如果启用）
      if (enableMath) {
        processedContent = processMathFormulas(processedContent)
      }

      // 使用marked渲染
      const html = marked(processedContent, markedOptions)
      
      return html
    } catch (error) {
      console.error('Markdown渲染失败:', error)
      return '<div class="render-error">Markdown渲染失败</div>'
    }
  }, [content, markedOptions, enableMath])

  /**
   * 处理链接点击事件
   */
  const handleLinkClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const target = event.target as HTMLElement
    
    if (target.tagName === 'A' && target.classList.contains('markdown-link')) {
      event.preventDefault()
      const href = target.getAttribute('data-href')
      
      if (href && onLinkClick) {
        onLinkClick(href, event)
      } else if (href) {
        // 默认行为：在新窗口打开外部链接
        if (href.startsWith('http') || href.startsWith('//')) {
          window.open(href, '_blank', 'noopener,noreferrer')
        } else {
          // 内部链接处理
          console.log('内部链接:', href)
        }
      }
    }
  }

  /**
   * 代码高亮处理
   */
  useEffect(() => {
    if (enableCodeHighlight && previewRef.current) {
      // 使用代码高亮服务处理所有代码块
      codeHighlightService.highlightAllInContainer(previewRef.current)
    }
  }, [renderedHtml, enableCodeHighlight])

  /**
   * 数学公式渲染处理
   */
  useEffect(() => {
    if (enableMath && previewRef.current) {
      // 使用数学公式渲染服务处理所有公式
      mathRendererService.renderAllInContainer(previewRef.current)
    }
  }, [renderedHtml, enableMath])

  if (loading) {
    return (
      <div className={`markdown-preview loading ${className}`}>
        <div className="flex items-center justify-center h-full">
          <Spin size="large" tip="渲染中..." />
        </div>
      </div>
    )
  }

  return (
    <div 
      ref={previewRef}
      className={`markdown-preview ${className}`}
      onClick={handleLinkClick}
      dangerouslySetInnerHTML={{ __html: renderedHtml }}
    />
  )
}

/**
 * 转义HTML字符
 */
function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * 生成标题ID
 */
function generateHeadingId(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\u4e00-\u9fff\s-]/g, '') // 保留中文、英文、数字、空格和连字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .replace(/^-|-$/g, '') // 移除首尾连字符
}

/**
 * 处理数学公式
 */
function processMathFormulas(content: string): string {
  // 处理行内数学公式 $...$
  content = content.replace(/\$([^$\n]+)\$/g, '<span class="math-formula inline-math">$1</span>')
  
  // 处理块级数学公式 $$...$$
  content = content.replace(/\$\$([^$]+)\$\$/g, '<div class="math-formula block-math">$1</div>')
  
  return content
}

/**
 * 提取目录信息
 */
export function extractTableOfContents(content: string): TocItem[] {
  const toc: TocItem[] = []
  const headingRegex = /^(#{1,6})\s+(.+)$/gm
  let match

  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length
    const text = match[2].trim()
    const id = generateHeadingId(text)
    
    toc.push({
      level,
      text,
      id,
      children: []
    })
  }

  return buildTocTree(toc)
}

/**
 * 构建目录树结构
 */
function buildTocTree(flatToc: TocItem[]): TocItem[] {
  const tree: TocItem[] = []
  const stack: TocItem[] = []

  for (const item of flatToc) {
    // 找到合适的父级
    while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {
      stack.pop()
    }

    if (stack.length === 0) {
      tree.push(item)
    } else {
      stack[stack.length - 1].children.push(item)
    }

    stack.push(item)
  }

  return tree
}

/**
 * 目录项接口
 */
export interface TocItem {
  level: number
  text: string
  id: string
  children: TocItem[]
}

export default MarkdownPreview
