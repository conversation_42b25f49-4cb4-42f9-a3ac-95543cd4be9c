import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Button, Typography, Statistic, List, Tag } from 'antd'
import {
  PlusOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  ClockCircleOutlined,
  StarOutlined,
  LinkOutlined,
  TagOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { DocumentType } from '@types/index'
import { documentManager, DocumentStats } from '@services/document/documentManager'
import DocumentList from '@components/document/DocumentList'

const { Title, Paragraph } = Typography

/**
 * 首页组件
 * 显示应用概览、快速操作和最近文档
 */
const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const [stats, setStats] = useState<DocumentStats | null>(null)
  const [loading, setLoading] = useState(true)

  /**
   * 加载统计信息
   */
  useEffect(() => {
    const loadStats = async () => {
      try {
        const documentStats = await documentManager.getDocumentStats()
        setStats(documentStats)
      } catch (error) {
        console.error('加载统计信息失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadStats()
  }, [])



  const quickActions = [
    {
      title: '新建文本笔记',
      description: '创建Markdown格式的文本笔记',
      icon: <FileTextOutlined className="text-2xl text-blue-500" />,
      action: () => navigate('/editor'),
      color: 'blue',
    },
    {
      title: '新建白板',
      description: '创建自由绘图的白板',
      icon: <BgColorsOutlined className="text-2xl text-green-500" />,
      action: () => navigate('/whiteboard'),
      color: 'green',
    },
    {
      title: '新建思维导图',
      description: '创建结构化的思维导图',
      icon: <NodeIndexOutlined className="text-2xl text-purple-500" />,
      action: () => navigate('/mindmap'),
      color: 'purple',
    },
    {
      title: '新建看板',
      description: '创建任务管理看板',
      icon: <ProjectOutlined className="text-2xl text-orange-500" />,
      action: () => navigate('/kanban'),
      color: 'orange',
    },
  ]

  const getDocumentIcon = (type: DocumentType) => {
    switch (type) {
      case DocumentType.TEXT:
        return <FileTextOutlined className="text-blue-500" />
      case DocumentType.WHITEBOARD:
        return <BgColorsOutlined className="text-green-500" />
      case DocumentType.MINDMAP:
        return <NodeIndexOutlined className="text-purple-500" />
      case DocumentType.KANBAN:
        return <ProjectOutlined className="text-orange-500" />
      default:
        return <FileTextOutlined />
    }
  }

  const getDocumentTypeName = (type: DocumentType) => {
    switch (type) {
      case DocumentType.TEXT:
        return '文本笔记'
      case DocumentType.WHITEBOARD:
        return '白板'
      case DocumentType.MINDMAP:
        return '思维导图'
      case DocumentType.KANBAN:
        return '看板'
      default:
        return '未知类型'
    }
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 欢迎区域 */}
      <div className="mb-8">
        <Title level={2} className="mb-2">
          欢迎使用多维度笔记应用
        </Title>
        <Paragraph className="text-gray-600 text-lg">
          一个现代化的知识管理工具，支持文本、白板、思维导图、看板等多种格式，
          并提供强大的关联系统帮助您构建个人知识网络。
        </Paragraph>
      </div>

      {/* 统计数据 */}
      {stats && (
        <Row gutter={[16, 16]} className="mb-8">
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总文档数"
                value={stats.totalDocuments}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="文本笔记"
                value={stats.documentsByType[DocumentType.TEXT]}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="标签数"
                value={stats.totalTags}
                prefix={<TagOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="最近活动"
                value={stats.recentActivity.length}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Row gutter={[24, 24]}>
        {/* 快速操作 */}
        <Col xs={24} lg={14}>
          <Card title="快速创建" className="h-full">
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={24} sm={12} key={index}>
                  <Card
                    hoverable
                    className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
                    onClick={action.action}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {action.icon}
                      </div>
                      <div className="flex-1">
                        <div className="font-semibold text-gray-800 mb-1">
                          {action.title}
                        </div>
                        <div className="text-sm text-gray-600">
                          {action.description}
                        </div>
                      </div>
                      <PlusOutlined className="text-gray-400" />
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* 最近文档 */}
        <Col xs={24} lg={10}>
          <Card
            title="最近文档"
            className="h-full"
            extra={
              <Button
                type="link"
                onClick={() => navigate('/documents')}
                className="p-0"
              >
                查看全部
              </Button>
            }
          >
            <DocumentList
              showCreateButton={false}
              showSearch={false}
              showFilters={false}
              pageSize={8}
              onDocumentCreate={(type) => {
                const routes = {
                  [DocumentType.TEXT]: '/editor',
                  [DocumentType.WHITEBOARD]: '/whiteboard',
                  [DocumentType.MINDMAP]: '/mindmap',
                  [DocumentType.KANBAN]: '/kanban'
                }
                navigate(routes[type])
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 功能介绍 */}
      <Row gutter={[24, 24]} className="mt-8">
        <Col xs={24}>
          <Card title="核心功能">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <div className="text-center p-4">
                  <FileTextOutlined className="text-4xl text-blue-500 mb-3" />
                  <Title level={4}>文本笔记</Title>
                  <Paragraph className="text-gray-600">
                    支持Markdown格式，提供丰富的编辑功能和实时预览
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div className="text-center p-4">
                  <BgColorsOutlined className="text-4xl text-green-500 mb-3" />
                  <Title level={4}>白板绘图</Title>
                  <Paragraph className="text-gray-600">
                    自由绘制、图形标注，支持无限画布和多层管理
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div className="text-center p-4">
                  <NodeIndexOutlined className="text-4xl text-purple-500 mb-3" />
                  <Title level={4}>思维导图</Title>
                  <Paragraph className="text-gray-600">
                    结构化思维整理，支持多种布局和丰富的样式
                  </Paragraph>
                </div>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <div className="text-center p-4">
                  <LinkOutlined className="text-4xl text-orange-500 mb-3" />
                  <Title level={4}>关联系统</Title>
                  <Paragraph className="text-gray-600">
                    双向链接、关系图谱，构建强大的知识网络
                  </Paragraph>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default HomePage
