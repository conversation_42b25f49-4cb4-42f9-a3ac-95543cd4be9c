/**
 * IndexedDB数据库结构定义
 * 定义了多维度笔记应用的数据库schema、表结构和索引
 */

import { DBSchema } from 'idb'
import { 
  BaseDocument, 
  DocumentLink, 
  DocumentType, 
  LinkType,
  UserSettings,
  SearchResult 
} from '@types/index'

/**
 * 数据库版本号
 * 每次schema变更时需要递增
 */
export const DATABASE_VERSION = 1

/**
 * 数据库名称
 */
export const DATABASE_NAME = 'multidimensional-notes-db'

/**
 * 数据库Schema接口定义
 * 扩展idb的DBSchema接口，定义所有对象存储的结构
 */
export interface NotesDBSchema extends DBSchema {
  // 文档存储 - 存储所有类型的文档
  documents: {
    key: string // 文档ID
    value: BaseDocument // 文档数据
    indexes: {
      'by-type': DocumentType // 按文档类型索引
      'by-created-at': Date // 按创建时间索引
      'by-updated-at': Date // 按更新时间索引
      'by-title': string // 按标题索引（用于搜索）
      'by-tags': string // 按标签索引（用于标签搜索）
    }
  }

  // 链接关系存储 - 存储文档间的关联关系
  links: {
    key: string // 链接ID
    value: DocumentLink // 链接数据
    indexes: {
      'by-source': string // 按源文档ID索引
      'by-target': string // 按目标文档ID索引
      'by-type': LinkType // 按链接类型索引
      'by-created-at': Date // 按创建时间索引
    }
  }

  // 标签存储 - 存储标签信息和使用统计
  tags: {
    key: string // 标签名称
    value: TagInfo // 标签信息
    indexes: {
      'by-count': number // 按使用次数索引
      'by-created-at': Date // 按创建时间索引
    }
  }

  // 搜索索引存储 - 存储全文搜索索引
  search_index: {
    key: string // 索引项ID (documentId + term)
    value: SearchIndexEntry // 搜索索引项
    indexes: {
      'by-document': string // 按文档ID索引
      'by-term': string // 按搜索词索引
      'by-score': number // 按相关性分数索引
    }
  }

  // 用户设置存储
  settings: {
    key: string // 设置键名
    value: UserSettings // 设置值
  }

  // 缓存存储 - 存储临时数据和缓存
  cache: {
    key: string // 缓存键
    value: CacheEntry // 缓存项
    indexes: {
      'by-expires-at': Date // 按过期时间索引
      'by-type': string // 按缓存类型索引
    }
  }

  // 文件附件存储 - 存储文件数据
  attachments: {
    key: string // 附件ID
    value: AttachmentData // 附件数据
    indexes: {
      'by-document': string // 按所属文档ID索引
      'by-type': string // 按文件类型索引
      'by-size': number // 按文件大小索引
      'by-created-at': Date // 按创建时间索引
    }
  }
}

/**
 * 标签信息接口
 */
export interface TagInfo {
  name: string // 标签名称
  count: number // 使用次数
  color?: string // 标签颜色
  description?: string // 标签描述
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
}

/**
 * 搜索索引项接口
 */
export interface SearchIndexEntry {
  id: string // 索引项ID
  documentId: string // 文档ID
  term: string // 搜索词
  score: number // 相关性分数
  positions: number[] // 词在文档中的位置
  field: string // 字段名称 (title, content, tags等)
  createdAt: Date // 创建时间
}

/**
 * 缓存项接口
 */
export interface CacheEntry {
  key: string // 缓存键
  value: any // 缓存值
  type: string // 缓存类型
  expiresAt: Date // 过期时间
  createdAt: Date // 创建时间
  size: number // 数据大小（字节）
}

/**
 * 附件数据接口
 */
export interface AttachmentData {
  id: string // 附件ID
  documentId: string // 所属文档ID
  name: string // 文件名
  type: string // MIME类型
  size: number // 文件大小（字节）
  data: ArrayBuffer // 文件数据
  thumbnail?: ArrayBuffer // 缩略图数据
  metadata: AttachmentMetadata // 附件元数据
  createdAt: Date // 创建时间
  updatedAt: Date // 更新时间
}

/**
 * 附件元数据接口
 */
export interface AttachmentMetadata {
  width?: number // 图片宽度
  height?: number // 图片高度
  duration?: number // 音视频时长
  encoding?: string // 编码格式
  checksum: string // 文件校验和
  [key: string]: any // 其他自定义元数据
}

/**
 * 数据库升级处理器类型
 */
export type UpgradeHandler = (
  db: IDBDatabase,
  oldVersion: number,
  newVersion: number,
  transaction: IDBTransaction
) => void

/**
 * 数据库升级处理器映射
 * 每个版本对应一个升级处理器
 */
export const upgradeHandlers: Record<number, UpgradeHandler> = {
  1: (db, oldVersion, newVersion, transaction) => {
    console.log(`数据库升级: v${oldVersion} -> v${newVersion}`)
    
    // 创建文档存储
    if (!db.objectStoreNames.contains('documents')) {
      const documentsStore = db.createObjectStore('documents', { keyPath: 'id' })
      
      // 创建索引
      documentsStore.createIndex('by-type', 'type', { unique: false })
      documentsStore.createIndex('by-created-at', 'createdAt', { unique: false })
      documentsStore.createIndex('by-updated-at', 'updatedAt', { unique: false })
      documentsStore.createIndex('by-title', 'title', { unique: false })
      documentsStore.createIndex('by-tags', 'tags', { unique: false, multiEntry: true })
      
      console.log('创建文档存储和索引')
    }

    // 创建链接存储
    if (!db.objectStoreNames.contains('links')) {
      const linksStore = db.createObjectStore('links', { keyPath: 'id' })
      
      // 创建索引
      linksStore.createIndex('by-source', 'sourceId', { unique: false })
      linksStore.createIndex('by-target', 'targetId', { unique: false })
      linksStore.createIndex('by-type', 'type', { unique: false })
      linksStore.createIndex('by-created-at', 'createdAt', { unique: false })
      
      console.log('创建链接存储和索引')
    }

    // 创建标签存储
    if (!db.objectStoreNames.contains('tags')) {
      const tagsStore = db.createObjectStore('tags', { keyPath: 'name' })
      
      // 创建索引
      tagsStore.createIndex('by-count', 'count', { unique: false })
      tagsStore.createIndex('by-created-at', 'createdAt', { unique: false })
      
      console.log('创建标签存储和索引')
    }

    // 创建搜索索引存储
    if (!db.objectStoreNames.contains('search_index')) {
      const searchStore = db.createObjectStore('search_index', { keyPath: 'id' })
      
      // 创建索引
      searchStore.createIndex('by-document', 'documentId', { unique: false })
      searchStore.createIndex('by-term', 'term', { unique: false })
      searchStore.createIndex('by-score', 'score', { unique: false })
      
      console.log('创建搜索索引存储和索引')
    }

    // 创建设置存储
    if (!db.objectStoreNames.contains('settings')) {
      db.createObjectStore('settings', { keyPath: 'key' })
      console.log('创建设置存储')
    }

    // 创建缓存存储
    if (!db.objectStoreNames.contains('cache')) {
      const cacheStore = db.createObjectStore('cache', { keyPath: 'key' })
      
      // 创建索引
      cacheStore.createIndex('by-expires-at', 'expiresAt', { unique: false })
      cacheStore.createIndex('by-type', 'type', { unique: false })
      
      console.log('创建缓存存储和索引')
    }

    // 创建附件存储
    if (!db.objectStoreNames.contains('attachments')) {
      const attachmentsStore = db.createObjectStore('attachments', { keyPath: 'id' })
      
      // 创建索引
      attachmentsStore.createIndex('by-document', 'documentId', { unique: false })
      attachmentsStore.createIndex('by-type', 'type', { unique: false })
      attachmentsStore.createIndex('by-size', 'size', { unique: false })
      attachmentsStore.createIndex('by-created-at', 'createdAt', { unique: false })
      
      console.log('创建附件存储和索引')
    }

    console.log('数据库初始化完成')
  }
}

/**
 * 获取数据库配置
 */
export const getDatabaseConfig = () => ({
  name: DATABASE_NAME,
  version: DATABASE_VERSION,
  upgradeHandlers
})
