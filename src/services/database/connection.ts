/**
 * IndexedDB数据库连接管理
 * 提供数据库连接、初始化、版本管理等功能
 */

import { openDB, IDBPDatabase } from 'idb'
import { NotesDBSchema, getDatabaseConfig } from './schema'

/**
 * 数据库连接管理器类
 * 单例模式，确保整个应用只有一个数据库连接实例
 */
class DatabaseConnection {
  private static instance: DatabaseConnection
  private db: IDBPDatabase<NotesDBSchema> | null = null
  private isInitializing = false
  private initPromise: Promise<IDBPDatabase<NotesDBSchema>> | null = null

  /**
   * 私有构造函数，防止外部直接实例化
   */
  private constructor() {}

  /**
   * 获取数据库连接管理器实例
   * @returns DatabaseConnection实例
   */
  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection()
    }
    return DatabaseConnection.instance
  }

  /**
   * 获取数据库连接
   * 如果连接不存在，则自动初始化
   * @returns Promise<IDBPDatabase<NotesDBSchema>>
   */
  public async getConnection(): Promise<IDBPDatabase<NotesDBSchema>> {
    if (this.db) {
      return this.db
    }

    if (this.isInitializing && this.initPromise) {
      return this.initPromise
    }

    return this.initialize()
  }

  /**
   * 初始化数据库连接
   * @returns Promise<IDBPDatabase<NotesDBSchema>>
   */
  private async initialize(): Promise<IDBPDatabase<NotesDBSchema>> {
    if (this.isInitializing && this.initPromise) {
      return this.initPromise
    }

    this.isInitializing = true
    
    this.initPromise = this.createConnection()
    
    try {
      this.db = await this.initPromise
      console.log('数据库连接初始化成功')
      return this.db
    } catch (error) {
      console.error('数据库连接初始化失败:', error)
      throw error
    } finally {
      this.isInitializing = false
      this.initPromise = null
    }
  }

  /**
   * 创建数据库连接
   * @returns Promise<IDBPDatabase<NotesDBSchema>>
   */
  private async createConnection(): Promise<IDBPDatabase<NotesDBSchema>> {
    const config = getDatabaseConfig()
    
    try {
      const db = await openDB<NotesDBSchema>(
        config.name,
        config.version,
        {
          upgrade: (db, oldVersion, newVersion, transaction) => {
            console.log(`数据库升级开始: v${oldVersion} -> v${newVersion}`)
            
            // 执行对应版本的升级处理器
            for (let version = oldVersion + 1; version <= (newVersion || config.version); version++) {
              const handler = config.upgradeHandlers[version]
              if (handler) {
                console.log(`执行版本 ${version} 的升级处理器`)
                handler(db as any, oldVersion, newVersion, transaction as any)
              }
            }
            
            console.log('数据库升级完成')
          },
          blocked: () => {
            console.warn('数据库升级被阻塞，请关闭其他标签页')
          },
          blocking: () => {
            console.warn('数据库连接阻塞了其他连接')
          },
          terminated: () => {
            console.error('数据库连接被意外终止')
            this.db = null
          }
        }
      )

      // 设置数据库事件监听器
      this.setupEventListeners(db)
      
      return db
    } catch (error) {
      console.error('创建数据库连接失败:', error)
      throw new Error(`数据库连接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 设置数据库事件监听器
   * @param db 数据库实例
   */
  private setupEventListeners(db: IDBPDatabase<NotesDBSchema>): void {
    // 监听数据库版本变更事件
    db.addEventListener('versionchange', () => {
      console.log('检测到数据库版本变更，关闭当前连接')
      this.close()
      // 提示用户刷新页面
      if (typeof window !== 'undefined') {
        alert('数据库已更新，请刷新页面以获取最新功能')
        window.location.reload()
      }
    })

    // 监听数据库错误事件
    db.addEventListener('error', (event) => {
      console.error('数据库错误:', event)
    })

    // 监听数据库关闭事件
    db.addEventListener('close', () => {
      console.log('数据库连接已关闭')
      this.db = null
    })
  }

  /**
   * 检查数据库连接状态
   * @returns boolean 连接是否有效
   */
  public isConnected(): boolean {
    return this.db !== null && !this.db.objectStoreNames.length === false
  }

  /**
   * 关闭数据库连接
   */
  public close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
      console.log('数据库连接已关闭')
    }
  }

  /**
   * 重新连接数据库
   * @returns Promise<IDBPDatabase<NotesDBSchema>>
   */
  public async reconnect(): Promise<IDBPDatabase<NotesDBSchema>> {
    console.log('重新连接数据库...')
    this.close()
    return this.initialize()
  }

  /**
   * 检查浏览器是否支持IndexedDB
   * @returns boolean 是否支持
   */
  public static isSupported(): boolean {
    return typeof window !== 'undefined' && 'indexedDB' in window
  }

  /**
   * 删除数据库
   * 注意：这将删除所有数据，请谨慎使用
   * @returns Promise<void>
   */
  public async deleteDatabase(): Promise<void> {
    const config = getDatabaseConfig()
    
    // 先关闭当前连接
    this.close()
    
    try {
      // 删除数据库
      await new Promise<void>((resolve, reject) => {
        const deleteRequest = indexedDB.deleteDatabase(config.name)
        
        deleteRequest.onsuccess = () => {
          console.log('数据库删除成功')
          resolve()
        }
        
        deleteRequest.onerror = () => {
          console.error('数据库删除失败:', deleteRequest.error)
          reject(deleteRequest.error)
        }
        
        deleteRequest.onblocked = () => {
          console.warn('数据库删除被阻塞，请关闭所有标签页')
        }
      })
    } catch (error) {
      console.error('删除数据库时发生错误:', error)
      throw error
    }
  }

  /**
   * 获取数据库信息
   * @returns Promise<DatabaseInfo>
   */
  public async getDatabaseInfo(): Promise<DatabaseInfo> {
    const db = await this.getConnection()
    const config = getDatabaseConfig()
    
    return {
      name: config.name,
      version: db.version,
      objectStoreNames: Array.from(db.objectStoreNames),
      isConnected: this.isConnected()
    }
  }

  /**
   * 执行数据库事务
   * @param storeNames 对象存储名称
   * @param mode 事务模式
   * @param callback 事务回调函数
   * @returns Promise<T>
   */
  public async transaction<T>(
    storeNames: (keyof NotesDBSchema)[],
    mode: IDBTransactionMode,
    callback: (tx: any) => Promise<T>
  ): Promise<T> {
    const db = await this.getConnection()
    const tx = db.transaction(storeNames as string[], mode)
    
    try {
      const result = await callback(tx)
      await tx.done
      return result
    } catch (error) {
      console.error('事务执行失败:', error)
      throw error
    }
  }
}

/**
 * 数据库信息接口
 */
export interface DatabaseInfo {
  name: string
  version: number
  objectStoreNames: string[]
  isConnected: boolean
}

/**
 * 导出数据库连接管理器实例
 */
export const dbConnection = DatabaseConnection.getInstance()

/**
 * 导出便捷方法
 */
export const getDB = () => dbConnection.getConnection()
export const isDBSupported = DatabaseConnection.isSupported
