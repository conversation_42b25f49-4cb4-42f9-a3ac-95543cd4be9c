import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { Layout, Menu, But<PERSON>, Drawer, theme } from 'antd'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FileTextOutlined,
  EditOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  ShareAltOutlined,
  SettingOutlined,
  HomeOutlined,
} from '@ant-design/icons'
import { useMediaQuery } from '@hooks/useMediaQuery'

const { Header, Sider, Content } = Layout

/**
 * 应用主布局组件
 * 提供统一的导航和布局结构
 */
const AppLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false)
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const isMobile = useMediaQuery('(max-width: 768px)')
  
  const {
    token: { colorBgContainer },
  } = theme.useToken()

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/documents',
      icon: <FileTextOutlined />,
      label: '文档管理',
    },
    {
      key: '/editor',
      icon: <EditOutlined />,
      label: '文本编辑器',
    },
    {
      key: '/whiteboard',
      icon: <BgColorsOutlined />,
      label: '白板',
    },
    {
      key: '/mindmap',
      icon: <NodeIndexOutlined />,
      label: '思维导图',
    },
    {
      key: '/kanban',
      icon: <ProjectOutlined />,
      label: '看板',
    },
    {
      key: '/graph',
      icon: <ShareAltOutlined />,
      label: '关系图谱',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
    if (isMobile) {
      setMobileDrawerOpen(false)
    }
  }

  const toggleSidebar = () => {
    if (isMobile) {
      setMobileDrawerOpen(!mobileDrawerOpen)
    } else {
      setCollapsed(!collapsed)
    }
  }

  // 侧边栏内容
  const sidebarContent = (
    <div className="h-full flex flex-col">
      {/* Logo区域 */}
      <div className="h-16 flex items-center justify-center border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <FileTextOutlined className="text-2xl text-primary-500" />
          {(!collapsed || isMobile) && (
            <span className="text-lg font-semibold text-gray-800">
              多维笔记
            </span>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="flex-1 overflow-y-auto">
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-none"
        />
      </div>

      {/* 底部信息 */}
      {(!collapsed || isMobile) && (
        <div className="p-4 border-t border-gray-200 text-center">
          <div className="text-xs text-gray-500">
            版本 v0.1.0
          </div>
        </div>
      )}
    </div>
  )

  return (
    <Layout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={240}
          collapsedWidth={80}
          className="bg-white border-r border-gray-200"
        >
          {sidebarContent}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title="导航菜单"
          placement="left"
          onClose={() => setMobileDrawerOpen(false)}
          open={mobileDrawerOpen}
          bodyStyle={{ padding: 0 }}
          width={240}
        >
          {sidebarContent}
        </Drawer>
      )}

      <Layout>
        {/* 顶部导航栏 */}
        <Header 
          style={{ 
            padding: 0, 
            background: colorBgContainer,
            borderBottom: '1px solid #f0f0f0'
          }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={toggleSidebar}
              className="text-base w-16 h-16"
            />
            
            {/* 面包屑导航 */}
            <div className="ml-4">
              <span className="text-gray-600 text-sm">
                {getCurrentPageTitle(location.pathname)}
              </span>
            </div>
          </div>

          {/* 右侧工具栏 */}
          <div className="flex items-center space-x-2 pr-6">
            {/* 这里可以添加搜索框、用户头像等 */}
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => navigate('/settings')}
              className="text-gray-600"
            />
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content className="bg-gray-50 overflow-hidden">
          <div className="h-full">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  )
}

/**
 * 根据路径获取当前页面标题
 */
function getCurrentPageTitle(pathname: string): string {
  const titleMap: Record<string, string> = {
    '/': '首页',
    '/editor': '文本编辑器',
    '/whiteboard': '白板',
    '/mindmap': '思维导图',
    '/kanban': '看板',
    '/graph': '关系图谱',
    '/settings': '设置',
  }

  // 处理带参数的路径
  for (const [path, title] of Object.entries(titleMap)) {
    if (pathname.startsWith(path)) {
      return title
    }
  }

  return '未知页面'
}

export default AppLayout
