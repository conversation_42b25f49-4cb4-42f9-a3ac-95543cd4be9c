/**
 * 文本编辑器组件
 * 集成Monaco Editor和Markdown预览的完整文本编辑解决方案
 */

import React, { useState, useCallback, useEffect, useRef } from 'react'
import { Layout, Button, Tooltip, Space, Divider, message } from 'antd'
import {
  SaveOutlined,
  EyeOutlined,
  EditOutlined,
  ColumnWidthOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  BoldOutlined,
  ItalicOutlined,
  LinkOutlined,
  PictureOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  CodeOutlined
} from '@ant-design/icons'
import MonacoEditor from './MonacoEditor'
import MarkdownPreview from './MarkdownPreview'
import { TextDocument, DocumentType } from '@types/index'
import { debounce } from '@utils/index'

const { Sider, Content } = Layout

/**
 * 编辑器视图模式
 */
export enum EditorViewMode {
  EDIT = 'edit',           // 仅编辑
  PREVIEW = 'preview',     // 仅预览
  SPLIT = 'split'          // 分屏显示
}

/**
 * 文本编辑器组件属性接口
 */
export interface TextEditorProps {
  document?: TextDocument // 文档数据
  onSave?: (document: TextDocument) => void // 保存回调
  onChange?: (content: string) => void // 内容变化回调
  onDocumentChange?: (document: Partial<TextDocument>) => void // 文档变化回调
  readOnly?: boolean // 是否只读
  autoSave?: boolean // 是否自动保存
  autoSaveDelay?: number // 自动保存延迟
  className?: string // CSS类名
  height?: string | number // 编辑器高度
}

/**
 * 文本编辑器组件
 * 提供完整的Markdown编辑和预览功能
 */
const TextEditor: React.FC<TextEditorProps> = ({
  document,
  onSave,
  onChange,
  onDocumentChange,
  readOnly = false,
  autoSave = true,
  autoSaveDelay = 2000,
  className = '',
  height = '100%'
}) => {
  const [viewMode, setViewMode] = useState<EditorViewMode>(EditorViewMode.SPLIT)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [content, setContent] = useState(document?.content.markdown || '')
  const [isSaving, setIsSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  
  const editorRef = useRef<any>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  /**
   * 防抖的保存函数
   */
  const debouncedSave = useCallback(
    debounce(async (newContent: string) => {
      if (!document || readOnly) return

      setIsSaving(true)
      try {
        const updatedDocument: TextDocument = {
          ...document,
          content: {
            ...document.content,
            markdown: newContent
          },
          updatedAt: new Date()
        }

        if (onSave) {
          await onSave(updatedDocument)
          setHasUnsavedChanges(false)
          console.log('文档自动保存成功')
        }
      } catch (error) {
        console.error('自动保存失败:', error)
        message.error('自动保存失败')
      } finally {
        setIsSaving(false)
      }
    }, autoSaveDelay),
    [document, onSave, readOnly, autoSaveDelay]
  )

  /**
   * 内容变化处理
   */
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent)
    setHasUnsavedChanges(true)

    // 触发onChange回调
    if (onChange) {
      onChange(newContent)
    }

    // 触发文档变化回调
    if (onDocumentChange) {
      onDocumentChange({
        content: {
          markdown: newContent
        }
      })
    }

    // 自动保存
    if (autoSave) {
      debouncedSave(newContent)
    }
  }, [onChange, onDocumentChange, autoSave, debouncedSave])

  /**
   * 手动保存
   */
  const handleManualSave = useCallback(async () => {
    if (!document || readOnly || isSaving) return

    setIsSaving(true)
    try {
      const updatedDocument: TextDocument = {
        ...document,
        content: {
          ...document.content,
          markdown: content
        },
        updatedAt: new Date()
      }

      if (onSave) {
        await onSave(updatedDocument)
        setHasUnsavedChanges(false)
        message.success('保存成功')
        console.log('文档手动保存成功')
      }
    } catch (error) {
      console.error('保存失败:', error)
      message.error('保存失败')
    } finally {
      setIsSaving(false)
    }
  }, [document, content, onSave, readOnly, isSaving])

  /**
   * 插入Markdown格式
   */
  const insertMarkdown = useCallback((prefix: string, suffix: string = '', placeholder: string = '') => {
    if (editorRef.current) {
      const editor = editorRef.current.getEditor()
      if (editor) {
        const selection = editor.getSelection()
        const selectedText = editor.getModel()?.getValueInRange(selection) || placeholder
        const newText = `${prefix}${selectedText}${suffix}`

        editor.executeEdits('insert-markdown', [{
          range: selection,
          text: newText,
          forceMoveMarkers: true
        }])

        editor.focus()
      }
    }
  }, [])

  /**
   * 工具栏按钮配置
   */
  const toolbarButtons = [
    {
      key: 'bold',
      icon: <BoldOutlined />,
      tooltip: '粗体 (Ctrl+B)',
      onClick: () => insertMarkdown('**', '**', '粗体文本')
    },
    {
      key: 'italic',
      icon: <ItalicOutlined />,
      tooltip: '斜体 (Ctrl+I)',
      onClick: () => insertMarkdown('*', '*', '斜体文本')
    },
    {
      key: 'code',
      icon: <CodeOutlined />,
      tooltip: '行内代码',
      onClick: () => insertMarkdown('`', '`', '代码')
    },
    {
      key: 'link',
      icon: <LinkOutlined />,
      tooltip: '链接 (Ctrl+K)',
      onClick: () => insertMarkdown('[', '](URL)', '链接文本')
    },
    {
      key: 'image',
      icon: <PictureOutlined />,
      tooltip: '图片',
      onClick: () => insertMarkdown('![', '](图片URL)', '图片描述')
    },
    {
      key: 'ul',
      icon: <UnorderedListOutlined />,
      tooltip: '无序列表',
      onClick: () => insertMarkdown('- ', '', '列表项')
    },
    {
      key: 'ol',
      icon: <OrderedListOutlined />,
      tooltip: '有序列表',
      onClick: () => insertMarkdown('1. ', '', '列表项')
    }
  ]

  /**
   * 切换全屏模式
   */
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen)
  }, [isFullscreen])

  /**
   * 处理键盘快捷键
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault()
            handleManualSave()
            break
          case 'Enter':
            if (event.shiftKey) {
              event.preventDefault()
              toggleFullscreen()
            }
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [handleManualSave, toggleFullscreen])

  /**
   * 监听编辑器保存事件
   */
  useEffect(() => {
    const handleEditorSave = (event: CustomEvent) => {
      handleManualSave()
    }

    window.addEventListener('editor-save', handleEditorSave as EventListener)
    return () => window.removeEventListener('editor-save', handleEditorSave as EventListener)
  }, [handleManualSave])

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="editor-toolbar flex items-center justify-between p-2 border-b border-gray-200 bg-white">
      <Space split={<Divider type="vertical" />}>
        {/* 格式化按钮 */}
        <Space>
          {toolbarButtons.map(button => (
            <Tooltip key={button.key} title={button.tooltip}>
              <Button
                type="text"
                icon={button.icon}
                onClick={button.onClick}
                disabled={readOnly}
                size="small"
              />
            </Tooltip>
          ))}
        </Space>

        {/* 视图模式切换 */}
        <Space>
          <Tooltip title="仅编辑">
            <Button
              type={viewMode === EditorViewMode.EDIT ? 'primary' : 'text'}
              icon={<EditOutlined />}
              onClick={() => setViewMode(EditorViewMode.EDIT)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="分屏显示">
            <Button
              type={viewMode === EditorViewMode.SPLIT ? 'primary' : 'text'}
              icon={<ColumnWidthOutlined />}
              onClick={() => setViewMode(EditorViewMode.SPLIT)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="仅预览">
            <Button
              type={viewMode === EditorViewMode.PREVIEW ? 'primary' : 'text'}
              icon={<EyeOutlined />}
              onClick={() => setViewMode(EditorViewMode.PREVIEW)}
              size="small"
            />
          </Tooltip>
        </Space>
      </Space>

      <Space>
        {/* 保存状态 */}
        {hasUnsavedChanges && (
          <span className="text-orange-500 text-sm">未保存</span>
        )}
        {isSaving && (
          <span className="text-blue-500 text-sm">保存中...</span>
        )}

        {/* 保存按钮 */}
        <Tooltip title="保存 (Ctrl+S)">
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleManualSave}
            loading={isSaving}
            disabled={readOnly || !hasUnsavedChanges}
            size="small"
          >
            保存
          </Button>
        </Tooltip>

        {/* 全屏按钮 */}
        <Tooltip title={isFullscreen ? '退出全屏' : '全屏 (Ctrl+Shift+Enter)'}>
          <Button
            type="text"
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={toggleFullscreen}
            size="small"
          />
        </Tooltip>
      </Space>
    </div>
  )

  /**
   * 渲染编辑器内容
   */
  const renderContent = () => {
    const editorProps = {
      ref: editorRef,
      value: content,
      onChange: handleContentChange,
      readOnly,
      height: '100%',
      autoSave: false, // 由父组件控制自动保存
      className: 'flex-1'
    }

    const previewProps = {
      content,
      className: 'flex-1 p-4 overflow-auto',
      enableMath: true,
      enableCodeHighlight: true
    }

    switch (viewMode) {
      case EditorViewMode.EDIT:
        return <MonacoEditor {...editorProps} />
      
      case EditorViewMode.PREVIEW:
        return <MarkdownPreview {...previewProps} />
      
      case EditorViewMode.SPLIT:
      default:
        return (
          <Layout className="h-full">
            <Content className="flex-1">
              <MonacoEditor {...editorProps} />
            </Content>
            <Sider width="50%" className="bg-white border-l border-gray-200">
              <MarkdownPreview {...previewProps} />
            </Sider>
          </Layout>
        )
    }
  }

  return (
    <div
      ref={containerRef}
      className={`text-editor ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''} ${className}`}
      style={{ height }}
    >
      <Layout className="h-full">
        {renderToolbar()}
        <div className="flex-1 overflow-hidden">
          {renderContent()}
        </div>
      </Layout>
    </div>
  )
}

export default TextEditor
