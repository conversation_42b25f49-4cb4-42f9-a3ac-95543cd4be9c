import React from 'react'
import { Card, Typography, Button } from 'antd'
import { ProjectOutlined, SaveOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

/**
 * 看板页面组件（占位）
 * 后续将实现完整的看板管理功能
 */
const KanbanPage: React.FC = () => {
  return (
    <div className="h-full p-6">
      <Card className="h-full">
        <div className="text-center py-20">
          <ProjectOutlined className="text-6xl text-orange-500 mb-4" />
          <Title level={2}>看板管理</Title>
          <Paragraph className="text-gray-600 mb-6">
            这里将实现强大的看板管理功能，包括：
          </Paragraph>
          <div className="text-left max-w-md mx-auto mb-8">
            <ul className="text-gray-600 space-y-2">
              <li>• 列表和卡片管理</li>
              <li>• 拖拽操作支持</li>
              <li>• 卡片详情编辑</li>
              <li>• 标签和优先级</li>
              <li>• 进度跟踪功能</li>
              <li>• 时间线视图</li>
            </ul>
          </div>
          <Button 
            type="primary" 
            size="large"
            icon={<SaveOutlined />}
            disabled
          >
            功能开发中...
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default KanbanPage
