import React from 'react'
import { Card, Typography, Button } from 'antd'
import { BgColorsOutlined, SaveOutlined } from '@ant-design/icons'

const { Title, Paragraph } = Typography

/**
 * 白板页面组件（占位）
 * 后续将实现完整的白板绘图功能
 */
const WhiteboardPage: React.FC = () => {
  return (
    <div className="h-full p-6">
      <Card className="h-full">
        <div className="text-center py-20">
          <BgColorsOutlined className="text-6xl text-green-500 mb-4" />
          <Title level={2}>白板绘图</Title>
          <Paragraph className="text-gray-600 mb-6">
            这里将实现强大的白板绘图功能，包括：
          </Paragraph>
          <div className="text-left max-w-md mx-auto mb-8">
            <ul className="text-gray-600 space-y-2">
              <li>• 自由绘制工具</li>
              <li>• 基础形状绘制</li>
              <li>• 文本标注功能</li>
              <li>• 图片插入编辑</li>
              <li>• 无限画布概念</li>
              <li>• 多层图层管理</li>
            </ul>
          </div>
          <Button 
            type="primary" 
            size="large"
            icon={<SaveOutlined />}
            disabled
          >
            功能开发中...
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default WhiteboardPage
